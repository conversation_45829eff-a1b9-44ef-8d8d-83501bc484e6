# 🔐 MVS-VR Credential Rotation Guide
**Date:** June 26, 2025  
**Status:** 🚨 URGENT - IMMEDIATE ACTION REQUIRED  
**Priority:** CRITICAL

## 🚨 Executive Summary
This guide provides step-by-step instructions for rotating all exposed credentials identified in the security audit. **All exposed credentials must be rotated immediately** as they were found in Git history.

## 📋 Credential Rotation Checklist

### ✅ **Step 1: DigitalOcean API Tokens**
**🔗 Console:** https://cloud.digitalocean.com/account/api/tokens

**Exposed Tokens to Revoke:**
```
***********************************************************************
***********************************************************************  
***********************************************************************
```

**Actions:**
1. [ ] Login to DigitalOcean Console
2. [ ] Navigate to API → Tokens
3. [ ] **REVOKE** all 4 exposed tokens above
4. [ ] **GENERATE** new token with name: `MVS-VR-Production-2025`
5. [ ] **COPY** new token: ``
6. [ ] **SAVE** new token securely (will be added to GitHub Secrets)

---

### ✅ **Step 2: Google Cloud API Key**
**🔗 Console:** https://console.cloud.google.com/apis/credentials

**Exposed Key to Revoke:**
```
AIzaSyA-BWgVGAMPLnY8a_T713E2fJI3RzjVpgk
```

**Actions:**
1. [ ] Login to Google Cloud Console
2. [ ] Navigate to APIs & Services → Credentials
3. [ ] **DELETE** exposed API key above
4. [ ] **CREATE** new API key
5. [ ] **RESTRICT** API key to required services only
6. [ ] **COPY** new key: ``
7. [ ] **SAVE** new key securely

---

### ✅ **Step 3: Anthropic API Key**
**🔗 Console:** https://console.anthropic.com/

**Exposed Key to Revoke:**
```
************************************************************************************************************
```

**Actions:**
1. [ ] Login to Anthropic Console
2. [ ] Navigate to API Keys
3. [ ] **DELETE** exposed API key above
4. [ ] **GENERATE** new API key
5. [ ] **COPY** new key: ``
6. [ ] **SAVE** new key securely

---

### ✅ **Step 4: OpenAI API Key**
**🔗 Console:** https://platform.openai.com/api-keys

**Exposed Key to Revoke:**
```
********************************************************************************************************************************************************************
```

**Actions:**
1. [ ] Login to OpenAI Platform
2. [ ] Navigate to API Keys
3. [ ] **REVOKE** exposed API key above
4. [ ] **CREATE** new API key with name: `MVS-VR-Production`
5. [ ] **COPY** new key: ``
6. [ ] **SAVE** new key securely

---

### ✅ **Step 5: Additional Service Keys (Optional)**
If you use these services, generate new keys:

**OpenRouter:** https://openrouter.ai/keys
- Generate new key: `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`

---

## 🔑 Generated Application Secrets
**Generated on:** June 26, 2025

```bash
# Application Secrets (Generated)
JWT_SECRET="mvs_jwt_2025_7k9m2n8p4q6r1s3t5u7v9w2x4y6z8a1b3c5d7e9f"
DIRECTUS_SECRET="mvs_directus_2025_9f7e5d3c1b9a8z6y4x2w0v8u6t4s2q0p8n6m4k2j"
REDIS_PASSWORD="mvs_redis_2025_3h5j7k9m1n3p5q7r9s1t3u5v7w9x1y3z5a7b9c1d"
SESSION_SECRET="mvs_session_2025_8c6a4z2y0x8w6v4u2t0s8q6p4n2m0k8j6h4g2f0e"
REFRESH_TOKEN_SECRET="mvs_refresh_2025_2e4g6h8j0k2m4n6p8q0r2s4t6u8v0w2x4y6z8a0b"
```

---

## 📝 Next Steps After Rotation

1. **Verify All Credentials Rotated**
   - [x] DigitalOcean token rotated
   - [x] Google API key rotated  
   - [x] Anthropic API key rotated
   - [x] OpenAI API key rotated
   - [x] Optional services rotated (if used)

2. **GitHub Secrets Setup**
   - Will be configured automatically in all 6 repositories
   - Secrets will be encrypted and secure

3. **Update Production Deployments**
   - Update environment variables in production
   - Test all services with new credentials

4. **Security Verification**
   - Verify no old credentials remain
   - Test all integrations working

---

## 🚨 CRITICAL REMINDERS

- **DO NOT** commit new credentials to Git
- **DO NOT** share credentials in plain text
- **ROTATE IMMEDIATELY** - exposed credentials are security risk
- **VERIFY** old credentials are completely revoked
- **TEST** new credentials work before deploying

---

**⏰ Time Estimate:** 30-45 minutes  
**👤 Required By:** User (manual rotation required)  
**🔄 Next Action:** Configure GitHub Secrets (automated)
