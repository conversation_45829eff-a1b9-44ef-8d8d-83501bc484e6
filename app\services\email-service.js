/**
 * MVS-VR Email Service
 * Handles email sending for vendor verification and notifications via Supabase
 */

const { createClient } = require('@supabase/supabase-js');

class EmailService {
  constructor() {
    // Initialize Supabase client for email sending
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
      this.isConfigured = true;
      console.log('✅ Email service configured with Supabase');
    } else {
      console.warn('⚠️ Supabase not configured. Email sending will be simulated.');
      this.isConfigured = false;
    }

    this.fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
    this.replyToEmail = process.env.EMAIL_REPLY_TO || '<EMAIL>';
  }

  /**
   * Send vendor verification email
   */
  async sendVendorVerificationEmail(vendorData, verificationToken) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'https://mvs.kanousai.com'}/vendor/verify?token=${verificationToken}&email=${encodeURIComponent(vendorData.email)}`;
    
    const emailData = {
      to: vendorData.email,
      from: {
        email: this.fromEmail,
        name: 'MVS-VR Platform'
      },
      replyTo: this.replyToEmail,
      subject: 'Verify Your MVS-VR Vendor Account',
      html: this.generateVerificationEmailHTML(vendorData, verificationUrl),
      text: this.generateVerificationEmailText(vendorData, verificationUrl)
    };

    return await this.sendEmail(emailData);
  }

  /**
   * Send welcome email after verification
   */
  async sendVendorWelcomeEmail(vendorData) {
    const emailData = {
      to: vendorData.email,
      from: {
        email: this.fromEmail,
        name: 'MVS-VR Platform'
      },
      replyTo: this.replyToEmail,
      subject: 'Welcome to MVS-VR - Your Account is Verified!',
      html: this.generateWelcomeEmailHTML(vendorData),
      text: this.generateWelcomeEmailText(vendorData)
    };

    return await this.sendEmail(emailData);
  }

  /**
   * Core email sending method using Supabase
   */
  async sendEmail(emailData) {
    try {
      if (this.isConfigured && this.supabase) {
        // Use Supabase Auth to send emails
        // Note: This uses Supabase's built-in email functionality
        console.log('📧 Sending email via Supabase Auth...');
        console.log(`   To: ${emailData.to}`);
        console.log(`   Subject: ${emailData.subject}`);

        // For vendor verification, we'll use Supabase's email system
        // This is a placeholder - actual implementation depends on Supabase setup
        const result = await this.supabase.auth.admin.inviteUserByEmail(emailData.to, {
          data: {
            vendor_verification: true,
            company_name: emailData.company_name || 'Unknown Company',
            verification_url: emailData.verification_url || ''
          }
        });

        if (result.error) {
          throw new Error(result.error.message);
        }

        console.log('✅ Email sent successfully via Supabase:', emailData.to);
        return {
          sent: true,
          messageId: result.data?.user?.id || 'supabase-' + Date.now(),
          message: 'Email sent successfully via Supabase',
          provider: 'supabase'
        };
      } else {
        // Simulate email sending for development/testing
        console.log('📧 Email Simulation (Supabase not configured):');
        console.log(`   To: ${emailData.to}`);
        console.log(`   Subject: ${emailData.subject}`);
        console.log(`   From: ${emailData.from.email || emailData.from}`);

        return {
          sent: true,
          messageId: 'simulated-' + Date.now(),
          message: 'Email simulated (Supabase not configured)',
          simulatedData: emailData,
          provider: 'simulation'
        };
      }
    } catch (error) {
      console.error('❌ Email sending failed:', error.message);
      return {
        sent: false,
        error: error.message,
        message: 'Failed to send email',
        provider: this.isConfigured ? 'supabase' : 'simulation'
      };
    }
  }

  /**
   * Generate HTML email template for verification
   */
  generateVerificationEmailHTML(vendorData, verificationUrl) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your MVS-VR Vendor Account</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Welcome to MVS-VR Platform</h1>
            <p>Virtual Reality Commerce Platform</p>
        </div>
        <div class="content">
            <h2>Hello ${vendorData.contact_name},</h2>
            <p>Thank you for registering <strong>${vendorData.company_name}</strong> with the MVS-VR Platform!</p>
            <p>To complete your registration and start creating VR experiences, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center;">
                <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </div>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">${verificationUrl}</p>
            
            <h3>What's Next?</h3>
            <ul>
                <li>Access your vendor dashboard</li>
                <li>Upload your products and 3D models</li>
                <li>Create immersive VR showrooms</li>
                <li>Manage your VR commerce experiences</li>
            </ul>
            
            <p>If you didn't create this account, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>© 2025 MVS-VR Platform. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${this.replyToEmail}">${this.replyToEmail}</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate plain text email for verification
   */
  generateVerificationEmailText(vendorData, verificationUrl) {
    return `
Welcome to MVS-VR Platform!

Hello ${vendorData.contact_name},

Thank you for registering ${vendorData.company_name} with the MVS-VR Platform!

To complete your registration and start creating VR experiences, please verify your email address by visiting:

${verificationUrl}

What's Next?
- Access your vendor dashboard
- Upload your products and 3D models
- Create immersive VR showrooms
- Manage your VR commerce experiences

If you didn't create this account, please ignore this email.

© 2025 MVS-VR Platform. All rights reserved.
Need help? Contact us at ${this.replyToEmail}
`;
  }

  /**
   * Generate HTML welcome email template
   */
  generateWelcomeEmailHTML(vendorData) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MVS-VR Platform</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Account Verified!</h1>
            <p>Welcome to MVS-VR Platform</p>
        </div>
        <div class="content">
            <h2>Congratulations ${vendorData.contact_name}!</h2>
            <p>Your vendor account for <strong>${vendorData.company_name}</strong> has been successfully verified and is now active.</p>
            
            <div style="text-align: center;">
                <a href="https://api.mvs.kanousai.com/admin" class="button">Access Your Dashboard</a>
            </div>
            
            <h3>Getting Started:</h3>
            <ol>
                <li><strong>Set Up Your Profile:</strong> Complete your company information</li>
                <li><strong>Upload Products:</strong> Add your 3D models and product catalog</li>
                <li><strong>Create VR Experiences:</strong> Build immersive showrooms</li>
                <li><strong>Go Live:</strong> Publish your VR commerce experiences</li>
            </ol>
            
            <p>Our team is here to help you succeed. Don't hesitate to reach out if you need assistance!</p>
        </div>
        <div class="footer">
            <p>© 2025 MVS-VR Platform. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${this.replyToEmail}">${this.replyToEmail}</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate plain text welcome email
   */
  generateWelcomeEmailText(vendorData) {
    return `
Account Verified - Welcome to MVS-VR Platform!

Congratulations ${vendorData.contact_name}!

Your vendor account for ${vendorData.company_name} has been successfully verified and is now active.

Access your dashboard: https://api.mvs.kanousai.com/admin

Getting Started:
1. Set Up Your Profile: Complete your company information
2. Upload Products: Add your 3D models and product catalog
3. Create VR Experiences: Build immersive showrooms
4. Go Live: Publish your VR commerce experiences

Our team is here to help you succeed. Don't hesitate to reach out if you need assistance!

© 2025 MVS-VR Platform. All rights reserved.
Need help? Contact us at ${this.replyToEmail}
`;
  }
}

module.exports = EmailService;
