# Vendor Dashboard Extension Deployment Guide

**Date:** 2025-06-30  
**Status:** Ready for Deployment  
**Target:** Production Directus Instance (https://api.mvs.kanousai.com)

## Overview

This guide provides step-by-step instructions to deploy the Vendor Dashboard Extension to the production Directus instance.

## Prerequisites

- SSH access to production server (**************)
- Directus instance running in Docker container
- Extension files ready for deployment

## Deployment Steps

### Automated Deployment (Recommended)

**Step 1: Run the Deployment Script**

```bash
# Execute the automated deployment script
# This script handles SSH connection, file copying, and service restart
bash scripts/deploy-dashboard-extension-production.sh
```

The automated script will:
- Connect to the production server
- Create the extension directory structure
- Copy extension files
- Set proper permissions
- Restart Directus to load the extension
- Verify deployment success

### Manual Deployment (Alternative)

**Step 1: Connect to Production Server**

```bash
# Connect via SSH using your preferred method
ssh root@**************
cd /opt/mvs-vr
```

**Note:** Ensure you have SSH access configured. If you don't have direct SSH access, use the automated deployment script which handles the connection.

### Step 2: Create Extension Directory

```bash
mkdir -p directus/extensions/interfaces/vendor-dashboard
```

### Step 3: Copy Extension Files

**Option A: Use the automated script (Recommended)**
```bash
# The deployment script handles file copying automatically
bash scripts/deploy-dashboard-extension-production.sh
```

**Option B: Manual file copy**
```bash
# From your local project directory, copy the extension file:
scp directus/extensions/interfaces/vendor-dashboard/index.js root@**************:/opt/mvs-vr/directus/extensions/interfaces/vendor-dashboard/

# Or if using Windows with PowerShell:
# Use WinSCP, FileZilla, or the deployment script instead
```

**Step 4: Set Proper Permissions**

```bash
chown -R root:root directus/extensions
chmod -R 755 directus/extensions
```

**Step 5: Restart Directus Container**

```bash
docker-compose restart directus
```

**Step 6: Verify Deployment**

```bash
# Check container status
docker-compose ps

# Check extension files
ls -la directus/extensions/interfaces/vendor-dashboard/

# Test Directus health
curl https://api.mvs.kanousai.com/server/health
```

### Production Deployment Script

For production deployment, use the provided script which automates all the above steps:

```bash
# Make the script executable (if needed)
chmod +x scripts/deploy-dashboard-extension-production.sh

# Run the deployment
bash scripts/deploy-dashboard-extension-production.sh

# The script will output progress and verify successful deployment
```

## Extension Features

The Vendor Dashboard Extension provides:

- **Vendor-specific Data Filtering**: Automatically filters data by vendor_id
- **VR Commerce Features**: VR experience management capabilities
- **Analytics Dashboard**: Vendor performance metrics
- **Product Management**: Vendor product catalog management
- **Showroom Management**: VR showroom configuration

## Verification Checklist

- [ ] Extension files copied to correct directory
- [ ] Proper file permissions set
- [ ] Directus container restarted successfully
- [ ] Directus health check passes
- [ ] Extension appears in Directus admin interface
- [ ] Vendor users can access dashboard features

## Troubleshooting

### Extension Not Loading

1. Check file permissions: `ls -la directus/extensions/interfaces/vendor-dashboard/`
2. Verify Directus logs: `docker logs mvs-directus-production`
3. Restart Directus: `docker-compose restart directus`

### Permission Issues

```bash
# Fix ownership
chown -R root:root directus/extensions

# Fix permissions
chmod -R 755 directus/extensions
```

### Container Issues

```bash
# Check container status
docker-compose ps

# View container logs
docker logs mvs-directus-production

# Restart if needed
docker-compose restart directus
```

## Post-Deployment Tasks

1. **Test Vendor Login**: Verify vendors can access the dashboard
2. **Check Data Filtering**: Ensure vendor-specific data isolation
3. **Test VR Features**: Verify VR experience management works
4. **Monitor Performance**: Check for any performance impacts

## Rollback Procedure

If issues occur, rollback by:

1. Remove extension directory:
   ```bash
   rm -rf directus/extensions/interfaces/vendor-dashboard
   ```

2. Restart Directus:
   ```bash
   docker-compose restart directus
   ```

## Next Steps

After successful deployment:

1. Configure vendor user permissions
2. Test vendor registration workflow
3. Verify email verification system
4. Update vendor onboarding documentation

---

**Deployment Status:** ✅ Ready for Production  
**Estimated Deployment Time:** 5-10 minutes  
**Risk Level:** Low (non-breaking change)
