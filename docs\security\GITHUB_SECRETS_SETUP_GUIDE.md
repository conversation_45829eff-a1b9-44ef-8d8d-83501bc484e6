# 🔐 GitHub Secrets Setup Guide
**Date:** June 26, 2025  
**Status:** 🚀 READY FOR IMPLEMENTATION  
**Priority:** HIGH

## 📋 Overview
This guide provides step-by-step instructions for setting up GitHub repository secrets across all MVS-VR repositories using both automated scripts and manual methods.

## 🎯 Target Repositories
- **mvs-vr-v2** (main workspace)
- **mvs-vr-backend** 
- **mvs-vr-directus**
- **mvs-vr-documentation**
- **mvs-vr-frontend**
- **mvs-vr-infrastructure**

## 🔑 Required Secrets

### **Core Infrastructure Secrets**
```bash
DIGITALOCEAN_ACCESS_TOKEN="***********************************************************************"
```

### **AI Service API Keys**
```bash
ANTHROPIC_API_KEY="************************************************************************************************************"
OPENAI_API_KEY="********************************************************************************************************************************************************************"
GOOGLE_API_KEY="AIzaSyCEWfrIROo1f6zEmQzIfRW9R-vNjtNYxKE"
OPENROUTER_API_KEY="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"     
```

### **Database Secrets**
```bash
SUPABASE_URL="https://hiyqiqbgiueyyvqoqhht.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNjM0MjQsImV4cCI6MjA2NjYzOTQyNH0.337gTeSzEiYtX-jfC2ZrkMFEK9tgiemKVBZRaWxuNgI"
SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA2MzQyNCwiZXhwIjoyMDY2NjM5NDI0fQ.b6mF8r57tvb_42iM0CkwXr6kvdOJM7zafunGlrj-rcg"
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

SUPABASE_PUBLISHABLE_KEY=sb_publishable_2O711Ba0ElopvbbmNFx6JA_OXyndjpP
SUPABASE_SECRET_KEY=sb_secret_CUZW5vZ4raTmqXc3DNWyQg_5ETQH7q6
```

### **Application Secrets (Pre-generated)**
```bash
JWT_SECRET="mvs_jwt_2025_7k9m2n8p4q6r1s3t5u7v9w2x4y6z8a1b3c5d7e9f"
DIRECTUS_SECRET="mvs_directus_2025_9f7e5d3c1b9a8z6y4x2w0v8u6t4s2q0p8n6m4k2j"
REDIS_PASSWORD="mvs_redis_2025_3h5j7k9m1n3p5q7r9s1t3u5v7w9x1y3z5a7b9c1d"
SESSION_SECRET="mvs_session_2025_8c6a4z2y0x8w6v4u2t0s8q6p4n2m0k8j6h4g2f0e"
REFRESH_TOKEN_SECRET="mvs_refresh_2025_2e4g6h8j0k2m4n6p8q0r2s4t6u8v0w2x4y6z8a0b"
```

## 🚀 Method 1: Automated Setup (Recommended)

### **Prerequisites**
1. Install GitHub CLI: https://cli.github.com/
2. Authenticate: `gh auth login`
3. Ensure you have rotated all external credentials first

### **Run Setup Script**
```powershell
# Navigate to project directory
cd C:\Users\<USER>\projects\mvs-vr-v2

# Run the automated setup script
.\scripts\setup-github-secrets.ps1 `
  -DigitalOceanToken "***********************************************************************" `
  -AnthropicApiKey "************************************************************************************************************" `
  -OpenAiApiKey "********************************************************************************************************************************************************************" `
  -GoogleApiKey "AIzaSyCEWfrIROo1f6zEmQzIfRW9R-vNjtNYxKE" `
  -OpenRouterApiKey "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" `
  -SupabaseUrl "https://hiyqiqbgiueyyvqoqhht.supabase.co" `
  -SupabaseAnonKey "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNjM0MjQsImV4cCI6MjA2NjYzOTQyNH0.337gTeSzEiYtX-jfC2ZrkMFEK9tgiemKVBZRaWxuNgI" `
  -SupabaseServiceKey "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA2MzQyNCwiZXhwIjoyMDY2NjM5NDI0fQ.b6mF8r57tvb_42iM0CkwXr6kvdOJM7zafunGlrj-rcg"
```

## 🔧 Method 2: Manual Setup

### **For Each Repository:**

1. **Navigate to Repository Settings**
   ```
   https://github.com/HDickenson/[repo-name]/settings/secrets/actions
   ```

2. **Add Each Secret**
   - Click "New repository secret"
   - Enter secret name (e.g., `DIGITALOCEAN_ACCESS_TOKEN`)
   - Enter secret value
   - Click "Add secret"

3. **Repeat for All Secrets**
   - Add all core infrastructure secrets
   - Add all AI service API keys
   - Add all database secrets
   - Add all application secrets

## 🔍 Verification Steps

### **1. Check Secrets Are Set**
```bash
# For each repository, run:
gh secret list --repo HDickenson/mvs-vr-v2
gh secret list --repo HDickenson/mvs-vr-backend
gh secret list --repo HDickenson/mvs-vr-directus
gh secret list --repo HDickenson/mvs-vr-documentation
gh secret list --repo HDickenson/mvs-vr-frontend
gh secret list --repo HDickenson/mvs-vr-infrastructure
```

### **2. Expected Output**
Each repository should show:
```
ANTHROPIC_API_KEY
DIGITALOCEAN_ACCESS_TOKEN
DIRECTUS_SECRET
GOOGLE_API_KEY
JWT_SECRET
OPENAI_API_KEY
REDIS_PASSWORD
REFRESH_TOKEN_SECRET
SESSION_SECRET
[Additional secrets based on configuration]
```

## 🔐 Security Best Practices

### **1. Secret Management**
- ✅ Never commit secrets to Git
- ✅ Use GitHub Secrets for CI/CD
- ✅ Rotate secrets regularly
- ✅ Use least privilege access
- ✅ Monitor secret usage

### **2. Access Control**
- ✅ Limit repository access
- ✅ Use environment-specific secrets
- ✅ Audit secret access regularly
- ✅ Remove unused secrets

### **3. Monitoring**
- ✅ Enable GitHub secret scanning
- ✅ Set up alerts for secret exposure
- ✅ Regular security audits
- ✅ Monitor for unauthorized access

## 📊 Implementation Checklist

### **Pre-Setup**
- [ ] All external credentials rotated
- [ ] GitHub CLI installed and authenticated
- [ ] Repository access verified

### **Setup Process**
- [ ] Run automated setup script OR manual setup
- [ ] Verify secrets in all 6 repositories
- [ ] Test secret access in workflows
- [ ] Update deployment processes

### **Post-Setup**
- [ ] Update production environment variables
- [ ] Test all services with new credentials
- [ ] Verify old credentials are revoked
- [ ] Update documentation

## 🚨 Troubleshooting

### **Common Issues**

**1. GitHub CLI Not Authenticated**
```bash
# Solution:
gh auth login
```

**2. Permission Denied**
```bash
# Solution: Ensure you have admin access to repositories
# Check: https://github.com/HDickenson/[repo]/settings/access
```

**3. Secret Not Updating**
```bash
# Solution: Delete and recreate the secret
gh secret delete SECRET_NAME --repo HDickenson/repo-name
gh secret set SECRET_NAME --body "new-value" --repo HDickenson/repo-name
```

## 📝 Next Steps After Setup

1. **Update CI/CD Workflows**
   - Ensure workflows reference correct secret names
   - Test deployment pipelines

2. **Update Production Deployments**
   - Update environment variables in production
   - Restart services with new credentials

3. **Security Verification**
   - Run security scans
   - Verify no old credentials remain
   - Test all integrations

4. **Documentation Updates**
   - Update deployment guides
   - Update developer onboarding docs

---

**⏰ Estimated Time:** 15-30 minutes  
**👤 Required By:** User (with admin access to repositories)  
**🔄 Next Action:** Update production deployments
