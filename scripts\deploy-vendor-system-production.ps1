# MVS-VR Vendor System Production Deployment Script
# Deploys the complete vendor system while preserving admin functionality

param(
    [string]$ServerIP = "**************",
    [string]$ProjectDir = "/opt/mvs-vr",
    [switch]$DryRun = $false,
    [switch]$SkipBackup = $false
)

Write-Host "🚀 MVS-VR Vendor System Production Deployment" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host "Target Server: $ServerIP" -ForegroundColor Yellow
Write-Host "Project Directory: $ProjectDir" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Cyan
}

# Function to execute SSH commands
function Invoke-SSHCommand {
    param($Command, $Description)
    
    Write-Host "`n📋 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would execute: $Command" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $result = ssh root@$ServerIP $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            Write-Host "Output: $result" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ SSH connection failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to copy files via SCP
function Copy-FileToServer {
    param($LocalPath, $RemotePath, $Description)
    
    Write-Host "`n📦 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would copy: $LocalPath -> root@${ServerIP}:$RemotePath" -ForegroundColor Cyan
        return $true
    }
    
    try {
        scp $LocalPath root@${ServerIP}:$RemotePath
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ File copy failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Verify admin system is working
Write-Host "`n🔍 Step 1: Verifying admin system status..." -ForegroundColor Blue
try {
    $adminHealth = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($adminHealth.status -eq "ok") {
        Write-Host "✅ Admin system is healthy and operational" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Admin system health check returned unexpected status" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Admin system health check failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Deployment aborted to preserve system integrity" -ForegroundColor Red
    exit 1
}

# Step 2: Create backup of current state
if (-not $SkipBackup) {
    $backupSuccess = Invoke-SSHCommand "mkdir -p $ProjectDir/backups/vendor-deployment-$(date +%Y%m%d_%H%M%S) && cp -r $ProjectDir/directus/extensions $ProjectDir/backups/vendor-deployment-$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || echo 'No existing extensions to backup'" "Creating system backup"
    if (-not $backupSuccess) {
        Write-Host "⚠️ Backup creation failed, but continuing deployment" -ForegroundColor Yellow
    }
}

# Step 3: Install Node.js dependencies for vendor system
$depsSuccess = Invoke-SSHCommand "cd $ProjectDir && npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto" "Installing vendor system dependencies"
if (-not $depsSuccess) {
    Write-Host "❌ Dependency installation failed" -ForegroundColor Red
    exit 1
}

# Step 4: Deploy vendor registration API
Write-Host "`n📦 Step 4: Deploying vendor registration API..." -ForegroundColor Blue
$apiSuccess = Copy-FileToServer "app/api/vendor-registration.js" "$ProjectDir/app/api/vendor-registration.js" "Copying vendor registration API"
if (-not $apiSuccess) {
    Write-Host "❌ API deployment failed" -ForegroundColor Red
    exit 1
}

$emailServiceSuccess = Copy-FileToServer "app/services/email-service.js" "$ProjectDir/app/services/email-service.js" "Copying email service"
if (-not $emailServiceSuccess) {
    Write-Host "❌ Email service deployment failed" -ForegroundColor Red
    exit 1
}

# Step 5: Deploy vendor dashboard extension
Write-Host "`n📦 Step 5: Deploying vendor dashboard extension..." -ForegroundColor Blue
$extensionDirSuccess = Invoke-SSHCommand "mkdir -p $ProjectDir/directus/extensions/interfaces/vendor-dashboard" "Creating extension directory"
if (-not $extensionDirSuccess) {
    Write-Host "❌ Extension directory creation failed" -ForegroundColor Red
    exit 1
}

$extensionSuccess = Copy-FileToServer "directus/extensions/interfaces/vendor-dashboard/index.js" "$ProjectDir/directus/extensions/interfaces/vendor-dashboard/index.js" "Copying vendor dashboard extension"
if (-not $extensionSuccess) {
    Write-Host "❌ Extension deployment failed" -ForegroundColor Red
    exit 1
}

# Step 6: Set proper permissions
$permissionsSuccess = Invoke-SSHCommand "chown -R root:root $ProjectDir/directus/extensions && chmod -R 755 $ProjectDir/directus/extensions && chown root:root $ProjectDir/app/api/vendor-registration.js && chmod 755 $ProjectDir/app/api/vendor-registration.js" "Setting file permissions"
if (-not $permissionsSuccess) {
    Write-Host "❌ Permission setting failed" -ForegroundColor Red
    exit 1
}

# Step 7: Update environment configuration
Write-Host "`n📝 Step 7: Updating environment configuration..." -ForegroundColor Blue
$envConfig = @"
# Vendor System Configuration
VENDOR_API_PORT=3001
NODE_ENV=production

# Supabase Configuration (for emails)
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5
"@

if (-not $DryRun) {
    $envConfig | Out-File -FilePath "temp-vendor-env.conf" -Encoding UTF8
    $envSuccess = Copy-FileToServer "temp-vendor-env.conf" "$ProjectDir/.env.vendor" "Copying vendor environment configuration"
    Remove-Item "temp-vendor-env.conf" -Force
    
    if (-not $envSuccess) {
        Write-Host "❌ Environment configuration failed" -ForegroundColor Red
        exit 1
    }
}

# Step 8: Start vendor registration API
Write-Host "`n🚀 Step 8: Starting vendor registration API..." -ForegroundColor Blue
$apiStartSuccess = Invoke-SSHCommand "cd $ProjectDir && nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 & echo \$! > vendor-api.pid" "Starting vendor registration API"
if (-not $apiStartSuccess) {
    Write-Host "❌ API startup failed" -ForegroundColor Red
    exit 1
}

# Step 9: Restart Directus to load extension (carefully)
Write-Host "`n🔄 Step 9: Restarting Directus to load vendor extension..." -ForegroundColor Blue
$directusRestartSuccess = Invoke-SSHCommand "cd $ProjectDir && docker-compose restart directus" "Restarting Directus container"
if (-not $directusRestartSuccess) {
    Write-Host "❌ Directus restart failed" -ForegroundColor Red
    exit 1
}

# Step 10: Wait for services to stabilize
Write-Host "`n⏳ Step 10: Waiting for services to stabilize..." -ForegroundColor Blue
Start-Sleep -Seconds 30

# Step 11: Verify deployment
Write-Host "`n✅ Step 11: Verifying deployment..." -ForegroundColor Blue

# Check admin system still works
try {
    $adminCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($adminCheck.status -eq "ok") {
        Write-Host "✅ Admin system remains operational" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Admin system health check shows issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Admin system verification failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Check vendor API
$vendorApiCheck = Invoke-SSHCommand "curl -f http://localhost:3001/health" "Testing vendor API health"
if ($vendorApiCheck) {
    Write-Host "✅ Vendor API is responding" -ForegroundColor Green
} else {
    Write-Host "⚠️ Vendor API health check failed" -ForegroundColor Yellow
}

# Check Directus admin interface
try {
    $directusCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/admin" -TimeoutSec 10
    if ($directusCheck -match "Directus") {
        Write-Host "✅ Directus admin interface is accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Directus admin interface check failed" -ForegroundColor Yellow
}

# Final summary
Write-Host "`n🎉 Vendor System Deployment Complete!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "✅ Vendor Registration API deployed and running" -ForegroundColor Green
Write-Host "✅ Vendor Dashboard Extension installed" -ForegroundColor Green
Write-Host "✅ Email Service configured with Supabase" -ForegroundColor Green
Write-Host "✅ Admin system preserved and operational" -ForegroundColor Green

Write-Host "`n🔗 Service URLs:" -ForegroundColor Blue
Write-Host "   Admin Portal: https://api.mvs.kanousai.com/admin" -ForegroundColor Cyan
Write-Host "   Directus Health: https://api.mvs.kanousai.com/server/health" -ForegroundColor Cyan
Write-Host "   Vendor API: http://localhost:3001/health (internal)" -ForegroundColor Cyan

Write-Host "`n📋 Next Steps:" -ForegroundColor Blue
Write-Host "   1. Test vendor registration flow" -ForegroundColor Cyan
Write-Host "   2. Verify email delivery via Supabase" -ForegroundColor Cyan
Write-Host "   3. Test vendor dashboard in Directus admin" -ForegroundColor Cyan
Write-Host "   4. Monitor system logs for any issues" -ForegroundColor Cyan

Write-Host "`n🚀 Vendor system is now live and ready for use!" -ForegroundColor Green
