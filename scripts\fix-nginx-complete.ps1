# MVS-VR Complete NGINX Fix and Configuration Script
# Implements the complete NGINX strategy to resolve all routing issues

param(
    [switch]$DryRun = $false,
    [switch]$SkipSSL = $false
)

Write-Host "🔧 MVS-VR Complete NGINX Configuration Fix" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Cyan
}

# SSH Configuration
$SSH_KEY = "C:\Users\<USER>\.ssh\mvs-vr-doctl"
$SSH_OPTIONS = "-i `"$SSH_KEY`" -o StrictHostKeyChecking=no"
$SERVER_IP = "**************"

# Helper function for SSH commands
function Invoke-SSH {
    param($Command, $Description)
    
    Write-Host "`n📋 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would execute: $Command" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $fullCommand = "ssh $SSH_OPTIONS root@$SERVER_IP `"$Command`""
        $result = Invoke-Expression $fullCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ SSH error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Helper function for file copying
function Copy-File {
    param($LocalPath, $RemotePath, $Description)
    
    Write-Host "`n📦 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would copy: $LocalPath -> $RemotePath" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $fullCommand = "scp $SSH_OPTIONS `"$LocalPath`" root@${SERVER_IP}:`"$RemotePath`""
        Invoke-Expression $fullCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Copy error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Backup current broken configuration
Write-Host "`n🔍 Step 1: Backing up current configuration..." -ForegroundColor Blue
$backup = Invoke-SSH "cd /etc/nginx && cp sites-available/default sites-available/default.broken.backup.$(date +%Y%m%d-%H%M%S) && cp sites-enabled/default sites-enabled/default.broken.backup.$(date +%Y%m%d-%H%M%S)" "Backing up broken configs"

if (-not $backup) {
    Write-Host "❌ Backup failed - stopping for safety" -ForegroundColor Red
    exit 1
}

# Step 2: Create proper NGINX configuration
Write-Host "`n📝 Step 2: Creating proper NGINX configuration..." -ForegroundColor Blue

$nginxConfig = @"
# MVS-VR API Server Configuration
# Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

# HTTP Server (redirects to HTTPS)
server {
    listen 80;
    listen [::]:80;
    server_name api.mvs.kanousai.com;
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files `$uri =404;
    }
    
    # Redirect all HTTP to HTTPS
    location / {
        return 301 https://`$server_name`$request_uri;
    }
}

# HTTPS Server (main configuration)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.mvs.kanousai.com;
    
    # SSL Configuration (will be updated by Certbot)
    ssl_certificate /etc/ssl/certs/ssl-cert-snakeoil.pem;
    ssl_certificate_key /etc/ssl/private/ssl-cert-snakeoil.key;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Main API routing (port 3001)
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
    }
    
    # Vendor API routing (port 3005)
    location /vendor/ {
        proxy_pass http://127.0.0.1:3005/;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
    }
    
    # Directus Admin (port 8055)
    location /admin/ {
        proxy_pass http://127.0.0.1:8055/admin/;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
    }
    
    # Health endpoints
    location /health {
        proxy_pass http://127.0.0.1:3001/health;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
    }
    
    location /server/health {
        proxy_pass http://127.0.0.1:8055/server/health;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
    }
    
    # Vendor health endpoint
    location /vendor/health {
        proxy_pass http://127.0.0.1:3005/health;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
    }
    
    # Default response
    location / {
        return 200 '{\"message\":\"MVS-VR API Server\",\"status\":\"operational\",\"timestamp\":\"$time_iso8601\",\"services\":[\"api\",\"vendor\",\"admin\"]}';
        add_header Content-Type application/json;
    }
}
"@

if (-not $DryRun) {
    $nginxConfig | Out-File -FilePath "temp-nginx-complete.conf" -Encoding UTF8
    $configDeploy = Copy-File "temp-nginx-complete.conf" "/etc/nginx/sites-available/api.mvs.kanousai.com" "Deploying new NGINX config"
    Remove-Item "temp-nginx-complete.conf" -Force
    
    if (-not $configDeploy) {
        Write-Host "❌ NGINX config deployment failed" -ForegroundColor Red
        exit 1
    }
}

# Step 3: Enable the new configuration
Write-Host "`n🔗 Step 3: Enabling new configuration..." -ForegroundColor Blue
$enable = Invoke-SSH "cd /etc/nginx/sites-enabled && rm -f default && ln -sf ../sites-available/api.mvs.kanousai.com api.mvs.kanousai.com" "Enabling new config"

if (-not $enable) {
    Write-Host "❌ Failed to enable new configuration" -ForegroundColor Red
    exit 1
}

# Step 4: Test NGINX configuration
Write-Host "`n🧪 Step 4: Testing NGINX configuration..." -ForegroundColor Blue
$test = Invoke-SSH "nginx -t" "Testing NGINX config"

if (-not $test) {
    Write-Host "❌ NGINX configuration test failed - rolling back" -ForegroundColor Red
    Invoke-SSH "cd /etc/nginx/sites-enabled && rm -f api.mvs.kanousai.com && ln -sf ../sites-available/default.broken.backup default" "Rolling back"
    exit 1
}

# Step 5: Reload NGINX
Write-Host "`n🔄 Step 5: Reloading NGINX..." -ForegroundColor Blue
$reload = Invoke-SSH "systemctl reload nginx" "Reloading NGINX"

if (-not $reload) {
    Write-Host "❌ NGINX reload failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ NGINX configuration completed successfully!" -ForegroundColor Green
Write-Host "`n📋 Next steps:" -ForegroundColor Blue
Write-Host "   1. Set up SSL certificates with: certbot --nginx -d api.mvs.kanousai.com" -ForegroundColor Cyan
Write-Host "   2. Test endpoints:" -ForegroundColor Cyan
Write-Host "      - https://api.mvs.kanousai.com/health" -ForegroundColor Cyan
Write-Host "      - https://api.mvs.kanousai.com/vendor/health" -ForegroundColor Cyan
Write-Host "      - https://api.mvs.kanousai.com/server/health" -ForegroundColor Cyan
