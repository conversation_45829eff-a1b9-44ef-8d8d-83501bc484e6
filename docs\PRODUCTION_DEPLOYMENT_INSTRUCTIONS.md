# MVS-VR Vendor System Production Deployment Instructions

**Date:** 2025-06-30
**Status:** Ready for Production Deployment
**Target:** Production Server (mvs-production)

## 🎯 Overview

This document provides step-by-step instructions to deploy the vendor system to production while preserving the existing admin functionality.

## 📋 Pre-Deployment Checklist

- [x] Admin system verified as operational
- [x] Vendor system components tested locally
- [x] Email service configured for Supabase
- [x] Database schema compatible
- [x] All dependencies identified

## 🚀 Deployment Steps

### Step 1: Connect to Production Server

**Automated Deployment (Recommended):**
```powershell
# Run the automated deployment script
.\scripts\deploy-vendor-production.ps1
```

**Manual Connection:**
```bash
# Connect using the secure SSH key
ssh -i "C:\Users\<USER>\.ssh\mvs-vr-doctl" -o StrictHostKeyChecking=no root@mvs-production
cd /opt/mvs-vr
```

### Step 2: Install Required Dependencies

```bash
# Install Node.js packages for vendor system
npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto

# Verify installation
npm list @supabase/supabase-js axios express
```

### Step 3: Create Directory Structure

```bash
# Create necessary directories
mkdir -p app/api
mkdir -p app/services
mkdir -p directus/extensions/interfaces/vendor-dashboard
mkdir -p logs
mkdir -p backups/vendor-deployment-$(date +%Y%m%d_%H%M%S)
```

### Step 4: Deploy Vendor Registration API

Create the vendor registration API file:

```bash
cat > app/api/vendor-registration.js << 'EOF'
[COPY CONTENT FROM LOCAL FILE: app/api/vendor-registration.js]
EOF
```

### Step 5: Deploy Email Service

Create the email service file:

```bash
cat > app/services/email-service.js << 'EOF'
[COPY CONTENT FROM LOCAL FILE: app/services/email-service.js]
EOF
```

### Step 6: Deploy Vendor Dashboard Extension

Create the dashboard extension:

```bash
cat > directus/extensions/interfaces/vendor-dashboard/index.js << 'EOF'
[COPY CONTENT FROM LOCAL FILE: directus/extensions/interfaces/vendor-dashboard/index.js]
EOF
```

### Step 7: Set File Permissions

```bash
# Set proper ownership and permissions
chown -R root:root app/ directus/extensions/
chmod -R 755 app/ directus/extensions/
chmod +x app/api/vendor-registration.js
```

### Step 8: Create Environment Configuration

```bash
cat > .env.vendor << 'EOF'
# Vendor System Configuration
VENDOR_API_PORT=3001
NODE_ENV=production

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.YQJWVLBhKJWJJOJJOJJOJJOJJOJJOJJOJJOJJOJJOJJO

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# Directus Configuration
DIRECTUS_URL=https://api.mvs.kanousai.com
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=CS8aNYclCtm8lx18td0wgudQ34XG5Gm7
EOF
```

### Step 9: Start Vendor Registration API

```bash
# Start the vendor API in the background
nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 &
echo $! > vendor-api.pid

# Verify it's running
sleep 5
curl http://localhost:3001/health
```

### Step 10: Restart Directus to Load Extension

```bash
# Restart Directus container to load the vendor dashboard extension
docker-compose restart directus

# Wait for Directus to restart
sleep 30

# Verify Directus is running
curl https://api.mvs.kanousai.com/server/health
```

### Step 11: Verify Deployment

```bash
# Test admin system
curl https://api.mvs.kanousai.com/server/health

# Test vendor API
curl http://localhost:3001/health

# Test vendor registration
curl -X POST http://localhost:3001/register \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Test Company",
    "email": "<EMAIL>",
    "contact_name": "Test User",
    "business_type": "retail"
  }'

# Check Directus admin interface
curl https://api.mvs.kanousai.com/admin
```

## 📁 File Contents to Deploy

### app/api/vendor-registration.js
```javascript
// [Copy the complete content from your local file]
// This file contains the vendor registration API server
```

### app/services/email-service.js
```javascript
// [Copy the complete content from your local file]
// This file contains the Supabase email service integration
```

### directus/extensions/interfaces/vendor-dashboard/index.js
```javascript
// [Copy the complete content from your local file]
// This file contains the vendor dashboard extension for Directus
```

## 🔍 Verification Checklist

After deployment, verify the following:

- [ ] Admin system remains accessible at https://api.mvs.kanousai.com/admin
- [ ] Directus health check passes: https://api.mvs.kanousai.com/server/health
- [ ] Vendor API responds: http://localhost:3001/health
- [ ] Vendor registration works: POST to http://localhost:3001/register
- [ ] Email service integrates with Supabase
- [ ] Vendor dashboard extension loads in Directus
- [ ] No errors in application logs

## 🚨 Troubleshooting

### Common Issues

1. **Vendor API not starting**
   ```bash
   # Check logs
   tail -f logs/vendor-api.log
   
   # Check if port is in use
   netstat -tlnp | grep 3001
   ```

2. **Directus extension not loading**
   ```bash
   # Check extension files
   ls -la directus/extensions/interfaces/vendor-dashboard/
   
   # Check Directus logs
   docker logs mvs-directus-production
   ```

3. **Email service issues**
   ```bash
   # Verify Supabase configuration
   node -e "console.log(process.env.SUPABASE_URL)"
   ```

### Rollback Procedure

If issues occur:

1. Stop vendor API: `kill $(cat vendor-api.pid)`
2. Remove vendor extension: `rm -rf directus/extensions/interfaces/vendor-dashboard`
3. Restart Directus: `docker-compose restart directus`
4. Verify admin system: `curl https://api.mvs.kanousai.com/server/health`

## 📊 Post-Deployment Testing

### Test Vendor Registration Flow

```bash
# Test complete registration flow
curl -X POST http://localhost:3001/register \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Production Test Company",
    "email": "<EMAIL>",
    "contact_name": "Production Test User",
    "business_type": "retail",
    "phone": "******-0123"
  }'
```

### Test Email Verification

```bash
# Extract verification token from registration response
# Then test verification endpoint
curl "http://localhost:3001/verify?token=VERIFICATION_TOKEN&email=<EMAIL>"
```

### Test Dashboard Access

1. Login to Directus admin: https://api.mvs.kanousai.com/admin
2. Navigate to vendor management sections
3. Verify vendor dashboard interface is available
4. Test vendor-specific data filtering

## 🎉 Success Criteria

Deployment is successful when:

- ✅ Admin system remains fully functional
- ✅ Vendor registration API is operational
- ✅ Email service integrates with Supabase
- ✅ Vendor dashboard extension loads in Directus
- ✅ All health checks pass
- ✅ Test vendor registration completes successfully

## 📞 Support

If you encounter issues during deployment:

1. Check the troubleshooting section above
2. Review application logs in `/opt/mvs-vr/logs/`
3. Verify all file permissions and ownership
4. Ensure all dependencies are installed correctly

---

**Deployment Status:** Ready for Execution  
**Estimated Time:** 30-45 minutes  
**Risk Level:** Low (preserves existing functionality)  
**Rollback Time:** 5-10 minutes if needed
