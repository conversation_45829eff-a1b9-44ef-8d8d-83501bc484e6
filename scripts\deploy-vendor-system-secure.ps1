# MVS-VR Vendor System Production Deployment Script
# Uses correct SSH method and API endpoints
# Deploys vendor system while preserving admin functionality

param(
    [switch]$DryRun = $false,
    [switch]$SkipBackup = $false
)

Write-Host "🚀 MVS-VR Vendor System Production Deployment" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Cyan
}

# Configuration
$SSH_KEY = "C:\Users\<USER>\.ssh\mvs-vr-doctl"
$PROJECT_DIR = "/opt/mvs-vr"

# Function to execute SSH commands using correct key
function Invoke-SecureSSH {
    param($Command, $Description)
    
    Write-Host "`n📋 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would execute: $Command" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $result = ssh -i $SSH_KEY -o StrictHostKeyChecking=no root@mvs-production $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            Write-Host "Output: $result" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ SSH connection failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to copy files via SCP using correct key
function Copy-SecureFile {
    param($LocalPath, $RemotePath, $Description)
    
    Write-Host "`n📦 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would copy: $LocalPath -> mvs-production:$RemotePath" -ForegroundColor Cyan
        return $true
    }
    
    try {
        scp -i $SSH_KEY -o StrictHostKeyChecking=no $LocalPath root@mvs-production:$RemotePath
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ File copy failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Verify admin system is working
Write-Host "`n🔍 Step 1: Verifying admin system status..." -ForegroundColor Blue
try {
    $adminHealth = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($adminHealth.status -eq "ok") {
        Write-Host "✅ Admin system is healthy and operational" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Admin system health check returned unexpected status" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Admin system health check failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Deployment aborted to preserve system integrity" -ForegroundColor Red
    exit 1
}

# Step 2: Test SSH connectivity
Write-Host "`n🔗 Step 2: Testing SSH connectivity..." -ForegroundColor Blue
$sshTest = Invoke-SecureSSH "echo 'SSH connection successful'" "Testing SSH connection"
if (-not $sshTest) {
    Write-Host "❌ SSH connection failed. Please check SSH key and server access." -ForegroundColor Red
    exit 1
}

# Step 3: Create backup of current state
if (-not $SkipBackup) {
    $backupSuccess = Invoke-SecureSSH "mkdir -p $PROJECT_DIR/backups/vendor-deployment-`$(date +%Y%m%d_%H%M%S) && cp -r $PROJECT_DIR/directus/extensions $PROJECT_DIR/backups/vendor-deployment-`$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || echo 'No existing extensions to backup'" "Creating system backup"
    if (-not $backupSuccess) {
        Write-Host "⚠️ Backup creation failed, but continuing deployment" -ForegroundColor Yellow
    }
}

# Step 4: Install Node.js dependencies for vendor system
$depsSuccess = Invoke-SecureSSH "cd $PROJECT_DIR && npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto" "Installing vendor system dependencies"
if (-not $depsSuccess) {
    Write-Host "❌ Dependency installation failed" -ForegroundColor Red
    exit 1
}

# Step 5: Create directory structure
$dirSuccess = Invoke-SecureSSH "mkdir -p $PROJECT_DIR/app/api $PROJECT_DIR/app/services $PROJECT_DIR/directus/extensions/interfaces/vendor-dashboard $PROJECT_DIR/logs" "Creating directory structure"
if (-not $dirSuccess) {
    Write-Host "❌ Directory creation failed" -ForegroundColor Red
    exit 1
}

# Step 6: Deploy vendor registration API
Write-Host "`n📦 Step 6: Deploying vendor registration API..." -ForegroundColor Blue
$apiSuccess = Copy-SecureFile "app/api/vendor-registration.js" "$PROJECT_DIR/app/api/vendor-registration.js" "Copying vendor registration API"
if (-not $apiSuccess) {
    Write-Host "❌ API deployment failed" -ForegroundColor Red
    exit 1
}

$emailServiceSuccess = Copy-SecureFile "app/services/email-service.js" "$PROJECT_DIR/app/services/email-service.js" "Copying email service"
if (-not $emailServiceSuccess) {
    Write-Host "❌ Email service deployment failed" -ForegroundColor Red
    exit 1
}

# Step 7: Deploy vendor dashboard extension
Write-Host "`n📦 Step 7: Deploying vendor dashboard extension..." -ForegroundColor Blue
$extensionSuccess = Copy-SecureFile "directus/extensions/interfaces/vendor-dashboard/index.js" "$PROJECT_DIR/directus/extensions/interfaces/vendor-dashboard/index.js" "Copying vendor dashboard extension"
if (-not $extensionSuccess) {
    Write-Host "❌ Extension deployment failed" -ForegroundColor Red
    exit 1
}

# Step 8: Set proper permissions
$permissionsSuccess = Invoke-SecureSSH "chown -R root:root $PROJECT_DIR/app $PROJECT_DIR/directus/extensions && chmod -R 755 $PROJECT_DIR/app $PROJECT_DIR/directus/extensions" "Setting file permissions"
if (-not $permissionsSuccess) {
    Write-Host "❌ Permission setting failed" -ForegroundColor Red
    exit 1
}

# Step 9: Update environment configuration for vendor API at /vendor endpoint
Write-Host "`n📝 Step 9: Updating environment configuration..." -ForegroundColor Blue
$envConfig = @"
# Vendor System Configuration
NODE_ENV=production

# API Configuration - Vendor API accessible at /vendor endpoint
VENDOR_API_BASE_PATH=/vendor
API_BASE_URL=https://api.mvs.kanousai.com

# Supabase Configuration (for emails)
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# Directus Configuration
DIRECTUS_URL=https://api.mvs.kanousai.com
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=CS8aNYclCtm8lx18td0wgudQ34XG5Gm7
"@

if (-not $DryRun) {
    $envConfig | Out-File -FilePath "temp-vendor-env.conf" -Encoding UTF8
    $envSuccess = Copy-SecureFile "temp-vendor-env.conf" "$PROJECT_DIR/.env.vendor" "Copying vendor environment configuration"
    Remove-Item "temp-vendor-env.conf" -Force
    
    if (-not $envSuccess) {
        Write-Host "❌ Environment configuration failed" -ForegroundColor Red
        exit 1
    }
}

# Step 10: Configure NGINX to route /vendor to vendor API
Write-Host "`n🔧 Step 10: Configuring NGINX routing for /vendor endpoint..." -ForegroundColor Blue
$nginxConfig = @"
# Add vendor API routing to existing NGINX configuration
location /vendor {
    proxy_pass http://localhost:3001;
    proxy_set_header Host `$host;
    proxy_set_header X-Real-IP `$remote_addr;
    proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto `$scheme;
    
    # Rate limiting for vendor endpoints
    limit_req zone=api burst=10 nodelay;
    
    # CORS headers
    add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
}
"@

if (-not $DryRun) {
    $nginxConfig | Out-File -FilePath "temp-nginx-vendor.conf" -Encoding UTF8
    $nginxSuccess = Copy-SecureFile "temp-nginx-vendor.conf" "$PROJECT_DIR/nginx-vendor-config.conf" "Copying NGINX vendor configuration"
    Remove-Item "temp-nginx-vendor.conf" -Force
    
    if ($nginxSuccess) {
        # Add the configuration to the main NGINX config
        $nginxUpdateSuccess = Invoke-SecureSSH "cat $PROJECT_DIR/nginx-vendor-config.conf >> /etc/nginx/sites-available/api.mvs.kanousai.com && nginx -t && systemctl reload nginx" "Updating NGINX configuration"
        if (-not $nginxUpdateSuccess) {
            Write-Host "⚠️ NGINX configuration update failed - vendor API will run on port 3001" -ForegroundColor Yellow
        }
    }
}

# Step 11: Start vendor registration API
Write-Host "`n🚀 Step 11: Starting vendor registration API..." -ForegroundColor Blue
$apiStartSuccess = Invoke-SecureSSH "cd $PROJECT_DIR && nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 & echo `$! > vendor-api.pid" "Starting vendor registration API"
if (-not $apiStartSuccess) {
    Write-Host "❌ API startup failed" -ForegroundColor Red
    exit 1
}

# Step 12: Restart Directus to load extension (carefully)
Write-Host "`n🔄 Step 12: Restarting Directus to load vendor extension..." -ForegroundColor Blue
$directusRestartSuccess = Invoke-SecureSSH "cd $PROJECT_DIR && docker-compose restart directus" "Restarting Directus container"
if (-not $directusRestartSuccess) {
    Write-Host "❌ Directus restart failed" -ForegroundColor Red
    exit 1
}

# Step 13: Wait for services to stabilize
Write-Host "`n⏳ Step 13: Waiting for services to stabilize..." -ForegroundColor Blue
Start-Sleep -Seconds 30

# Step 14: Verify deployment
Write-Host "`n✅ Step 14: Verifying deployment..." -ForegroundColor Blue

# Check admin system still works
try {
    $adminCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($adminCheck.status -eq "ok") {
        Write-Host "✅ Admin system remains operational" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Admin system health check shows issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Admin system verification failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Check vendor API via /vendor endpoint
try {
    $vendorCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/vendor/health" -TimeoutSec 10
    if ($vendorCheck.status -eq "ok") {
        Write-Host "✅ Vendor API is accessible via /vendor endpoint" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Vendor API accessible but returned unexpected status" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Vendor API not accessible via /vendor endpoint - check NGINX configuration" -ForegroundColor Yellow
    
    # Fallback: Check if API is running on port 3001
    $portCheck = Invoke-SecureSSH "curl -f http://localhost:3001/health" "Testing vendor API on port 3001"
    if ($portCheck) {
        Write-Host "✅ Vendor API is running on port 3001" -ForegroundColor Green
    }
}

# Check Directus admin interface
try {
    $directusCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/admin" -TimeoutSec 10
    if ($directusCheck -match "Directus") {
        Write-Host "✅ Directus admin interface is accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Directus admin interface check failed" -ForegroundColor Yellow
}

# Final summary
Write-Host "`n🎉 Vendor System Deployment Complete!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "✅ Vendor Registration API deployed and running" -ForegroundColor Green
Write-Host "✅ Vendor Dashboard Extension installed" -ForegroundColor Green
Write-Host "✅ Email Service configured with Supabase" -ForegroundColor Green
Write-Host "✅ Admin system preserved and operational" -ForegroundColor Green

Write-Host "`n🔗 Service URLs:" -ForegroundColor Blue
Write-Host "   Admin Portal: https://api.mvs.kanousai.com/admin" -ForegroundColor Cyan
Write-Host "   Vendor API: https://api.mvs.kanousai.com/vendor" -ForegroundColor Cyan
Write-Host "   Directus Health: https://api.mvs.kanousai.com/server/health" -ForegroundColor Cyan

Write-Host "`n📋 Next Steps:" -ForegroundColor Blue
Write-Host "   1. Test vendor registration: POST to https://api.mvs.kanousai.com/vendor/register" -ForegroundColor Cyan
Write-Host "   2. Verify email delivery via Supabase" -ForegroundColor Cyan
Write-Host "   3. Test vendor dashboard in Directus admin" -ForegroundColor Cyan
Write-Host "   4. Monitor system logs for any issues" -ForegroundColor Cyan

Write-Host "`n🚀 Vendor system is now live at https://api.mvs.kanousai.com/vendor!" -ForegroundColor Green
