/**
 * MVS-VR v2 Vendor Registration API
 * Handles vendor registration with email verification
 */

const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const EmailService = require('../services/email-service');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || [
    'https://mvs.kanousai.com',
    'https://api.mvs.kanousai.com',
    'http://localhost:3000'
  ],
  credentials: true
}));

// Rate limiting for registration endpoint
const registrationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many registration attempts, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Configuration
const DIRECTUS_URL = process.env.DIRECTUS_URL || 'https://api.mvs.kanousai.com';
const ADMIN_EMAIL = process.env.DIRECTUS_ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.DIRECTUS_ADMIN_PASSWORD || 'CS8aNYclCtm8lx18td0wgudQ34XG5Gm7';

class VendorRegistrationAPI {
  constructor() {
    this.adminToken = null;
    this.emailService = new EmailService();
  }

  async authenticate() {
    try {
      const response = await axios.post(`${DIRECTUS_URL}/auth/login`, {
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD
      });

      this.adminToken = response.data.data.access_token;
      return true;
    } catch (error) {
      console.error('Authentication failed:', error.response?.data || error.message);
      return false;
    }
  }

  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  validateRegistrationData(data) {
    const errors = [];

    // Required fields validation
    if (!data.company_name || data.company_name.trim().length < 2) {
      errors.push('Company name is required and must be at least 2 characters');
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Valid email address is required');
    }

    if (!data.contact_name || data.contact_name.trim().length < 2) {
      errors.push('Contact name is required and must be at least 2 characters');
    }

    if (!data.business_type) {
      errors.push('Business type is required');
    }

    // Optional but validated fields
    if (data.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Invalid phone number format');
    }

    if (data.website && !/^https?:\/\/.+\..+/.test(data.website)) {
      errors.push('Invalid website URL format');
    }

    return errors;
  }

  async checkExistingVendor(email, companyName) {
    try {
      // Check for existing vendor by email
      const emailCheck = await axios.get(`${DIRECTUS_URL}/items/vendors?filter[email][_eq]=${encodeURIComponent(email)}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (emailCheck.data.data.length > 0) {
        return { exists: true, reason: 'Email already registered' };
      }

      // Check for existing vendor by company name
      const companyCheck = await axios.get(`${DIRECTUS_URL}/items/vendors?filter[company_name][_eq]=${encodeURIComponent(companyName)}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (companyCheck.data.data.length > 0) {
        return { exists: true, reason: 'Company name already registered' };
      }

      return { exists: false };
    } catch (error) {
      console.error('Error checking existing vendor:', error.response?.data || error.message);
      throw new Error('Unable to verify vendor uniqueness');
    }
  }

  async createVendorRecord(registrationData, verificationToken) {
    try {
      // First, create a user account for the vendor
      const userData = {
        email: registrationData.email.toLowerCase().trim(),
        password: crypto.randomBytes(32).toString('hex'), // Random password, will be reset
        first_name: registrationData.contact_name.split(' ')[0] || 'Vendor',
        last_name: registrationData.contact_name.split(' ').slice(1).join(' ') || 'User',
        role: null, // Will be set to vendor role later
        status: 'invited'
      };

      const userResponse = await axios.post(`${DIRECTUS_URL}/users`, userData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      const userId = userResponse.data.data.id;

      const vendorData = {
        user_id: userId,
        company_name: registrationData.company_name.trim(),
        email: registrationData.email.toLowerCase().trim(),
        contact_name: registrationData.contact_name.trim(),
        business_type: registrationData.business_type,
        phone: registrationData.phone || null,
        website: registrationData.website || null,
        description: registrationData.description || null,
        status: 'pending',
        verification_token: verificationToken,
        verification_status: 'pending',
        onboarding_completed: false,
        subscription_tier: 'basic',
        contact_info: {
          email: registrationData.email.toLowerCase().trim(),
          phone: registrationData.phone || null,
          address: registrationData.address || null
        },
        profile: {
          contact_name: registrationData.contact_name.trim(),
          business_type: registrationData.business_type,
          website: registrationData.website || null,
          description: registrationData.description || null,
          registration_date: new Date().toISOString()
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const response = await axios.post(`${DIRECTUS_URL}/items/vendors`, vendorData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      return response.data.data;
    } catch (error) {
      console.error('Error creating vendor record:', error.response?.data || error.message);
      throw new Error('Unable to create vendor record');
    }
  }

  async sendVerificationEmail(vendorData, verificationToken) {
    try {
      const result = await this.emailService.sendVendorVerificationEmail(vendorData, verificationToken);

      if (result.sent) {
        console.log('✅ Verification email sent successfully to:', vendorData.email);
      } else {
        console.error('❌ Failed to send verification email:', result.error);
      }

      return result;
    } catch (error) {
      console.error('❌ Email service error:', error.message);
      return {
        sent: false,
        error: error.message,
        message: 'Failed to send verification email'
      };
    }
  }

  async registerVendor(req, res) {
    try {
      // Authenticate with Directus
      const authenticated = await this.authenticate();
      if (!authenticated) {
        return res.status(500).json({
          success: false,
          error: 'Service temporarily unavailable',
          code: 'SERVICE_ERROR'
        });
      }

      // Validate registration data
      const validationErrors = this.validateRegistrationData(req.body);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validationErrors
        });
      }

      // Check for existing vendor
      const existingCheck = await this.checkExistingVendor(req.body.email, req.body.company_name);
      if (existingCheck.exists) {
        return res.status(409).json({
          success: false,
          error: existingCheck.reason,
          code: 'VENDOR_EXISTS'
        });
      }

      // Generate verification token
      const verificationToken = this.generateVerificationToken();

      // Create vendor record
      const vendorRecord = await this.createVendorRecord(req.body, verificationToken);

      // Send verification email
      const emailResult = await this.sendVerificationEmail(vendorRecord, verificationToken);

      // Return success response
      res.status(201).json({
        success: true,
        message: 'Vendor registration initiated successfully',
        data: {
          vendor_id: vendorRecord.id,
          company_name: vendorRecord.company_name,
          email: vendorRecord.email,
          status: vendorRecord.status,
          verification_required: true,
          next_steps: [
            'Check your email for verification instructions',
            'Click the verification link to activate your account',
            'Complete your vendor profile setup'
          ]
        },
        email_status: emailResult
      });

    } catch (error) {
      console.error('Vendor registration error:', error);
      res.status(500).json({
        success: false,
        error: 'Registration failed',
        code: 'REGISTRATION_ERROR',
        message: error.message
      });
    }
  }

  async verifyVendor(req, res) {
    try {
      const { token, email } = req.query;

      if (!token || !email) {
        return res.status(400).json({
          success: false,
          error: 'Verification token and email are required',
          code: 'MISSING_PARAMETERS'
        });
      }

      // Authenticate with Directus
      const authenticated = await this.authenticate();
      if (!authenticated) {
        return res.status(500).json({
          success: false,
          error: 'Service temporarily unavailable',
          code: 'SERVICE_ERROR'
        });
      }

      // Find vendor by email and token
      const response = await axios.get(`${DIRECTUS_URL}/items/vendors?filter[email][_eq]=${encodeURIComponent(email)}&filter[verification_token][_eq]=${token}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      if (response.data.data.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Invalid verification token or email',
          code: 'INVALID_VERIFICATION'
        });
      }

      const vendor = response.data.data[0];

      if (vendor.verification_status === 'verified') {
        return res.status(200).json({
          success: true,
          message: 'Vendor already verified',
          data: {
            vendor_id: vendor.id,
            company_name: vendor.company_name,
            status: vendor.status
          }
        });
      }

      // Update vendor verification status
      await axios.patch(`${DIRECTUS_URL}/items/vendors/${vendor.id}`, {
        verification_status: 'verified',
        status: 'approved',
        verification_token: null,
        updated_at: new Date().toISOString()
      }, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      // Send welcome email
      try {
        await this.emailService.sendVendorWelcomeEmail(vendor);
        console.log('✅ Welcome email sent to:', vendor.email);
      } catch (error) {
        console.error('⚠️ Failed to send welcome email:', error.message);
        // Don't fail the verification if welcome email fails
      }

      res.status(200).json({
        success: true,
        message: 'Vendor verification completed successfully',
        data: {
          vendor_id: vendor.id,
          company_name: vendor.company_name,
          status: 'approved',
          next_steps: [
            'Your vendor account is now active',
            'You can now access the vendor portal',
            'Complete your profile setup to get started'
          ]
        }
      });

    } catch (error) {
      console.error('Vendor verification error:', error);
      res.status(500).json({
        success: false,
        error: 'Verification failed',
        code: 'VERIFICATION_ERROR',
        message: error.message
      });
    }
  }
}

// Initialize API
const vendorAPI = new VendorRegistrationAPI();

// Routes
app.post('/register', registrationLimiter, (req, res) => vendorAPI.registerVendor(req, res));
app.get('/verify', (req, res) => vendorAPI.verifyVendor(req, res));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'vendor-registration-api' });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  });
});

const PORT = process.env.VENDOR_API_PORT || 3001;
const BASE_PATH = process.env.VENDOR_API_BASE_PATH || '';

app.listen(PORT, () => {
  console.log(`🚀 Vendor Registration API running on port ${PORT}`);
  console.log(`📋 Available endpoints:`);
  if (BASE_PATH) {
    console.log(`   POST ${BASE_PATH}/register - Register new vendor`);
    console.log(`   GET ${BASE_PATH}/verify - Verify vendor email`);
    console.log(`   GET ${BASE_PATH}/health - Health check`);
    console.log(`🌐 Public URL: https://api.mvs.kanousai.com${BASE_PATH}`);
  } else {
    console.log(`   POST /register - Register new vendor`);
    console.log(`   GET /verify - Verify vendor email`);
    console.log(`   GET /health - Health check`);
    console.log(`🌐 Local URL: http://localhost:${PORT}`);
  }
});

module.exports = app;
