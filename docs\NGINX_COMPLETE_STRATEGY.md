# MVS-VR Complete NGINX Configuration Strategy

**Date:** 2025-06-30  
**Status:** 🚧 IMPLEMENTATION REQUIRED  
**Priority:** 🔥 CRITICAL - Main deployment blocker

## 🎯 Current Situation Analysis

### ❌ **Current Issues**
1. **Broken NGINX Config**: Current `/etc/nginx/sites-available/default` has syntax errors and malformed proxy headers
2. **No HTTPS/SSL**: No SSL certificates configured, only HTTP on port 80
3. **Missing Vendor Endpoint**: No `/vendor` proxy configuration for the vendor API
4. **Port Conflicts**: Multiple services running on different ports without proper routing
5. **No Load Balancing**: Single point of failure for API services

### 📊 **Current Port Mapping**
- **Port 80**: NGINX HTTP (broken config)
- **Port 3001**: Main API container (mvs-api-production)
- **Port 3002**: Grafana (mvs-grafana-enhanced)
- **Port 3003**: Unknown service (referenced in current NGINX)
- **Port 3004**: Next.js server
- **Port 3005**: Vendor API (newly deployed) ✅
- **Port 8055**: Directus CMS

## 🚀 **Complete NGINX Strategy**

### **Phase 1: Clean Up Current Configuration**
1. **Backup existing configs**
2. **Remove broken configurations**
3. **Create clean base configuration**

### **Phase 2: SSL/HTTPS Setup**
1. **Install Certbot**
2. **Generate SSL certificates for api.mvs.kanousai.com**
3. **Configure HTTPS redirects**

### **Phase 3: Service Routing Configuration**
1. **Main API routing** (`/api/` → port 3001)
2. **Vendor API routing** (`/vendor/` → port 3005)
3. **Admin/Directus routing** (`/admin/` → port 8055)
4. **Health endpoints** (`/health`, `/server/health`)

### **Phase 4: Security & Performance**
1. **Security headers**
2. **Rate limiting**
3. **CORS configuration**
4. **Gzip compression**

### **Phase 5: Testing & Validation**
1. **Endpoint testing**
2. **SSL verification**
3. **Load testing**
4. **Monitoring setup**

## 📋 **Implementation Steps**

### **Step 1: Emergency Cleanup**
```bash
# Backup current configs
cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.broken.backup
cp /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.broken.backup

# Test current config (will fail)
nginx -t

# Create minimal working config
```

### **Step 2: Create Proper Base Configuration**
```nginx
# /etc/nginx/sites-available/api.mvs.kanousai.com
server {
    listen 80;
    server_name api.mvs.kanousai.com;
    
    # Redirect all HTTP to HTTPS (after SSL setup)
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.mvs.kanousai.com;
    
    # SSL Configuration (to be added in Phase 2)
    
    # Main API routing
    location /api/ {
        proxy_pass http://127.0.0.1:3001/;
        include /etc/nginx/proxy_params;
    }
    
    # Vendor API routing
    location /vendor/ {
        proxy_pass http://127.0.0.1:3005/;
        include /etc/nginx/proxy_params;
    }
    
    # Directus Admin
    location /admin/ {
        proxy_pass http://127.0.0.1:8055/admin/;
        include /etc/nginx/proxy_params;
    }
    
    # Health endpoints
    location /health {
        proxy_pass http://127.0.0.1:3001/health;
        include /etc/nginx/proxy_params;
    }
    
    location /server/health {
        proxy_pass http://127.0.0.1:8055/server/health;
        include /etc/nginx/proxy_params;
    }
    
    # Default response
    location / {
        return 200 '{"message":"MVS-VR API Server","status":"operational","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }
}
```

### **Step 3: SSL Certificate Setup**
```bash
# Install Certbot
apt update && apt install -y certbot python3-certbot-nginx

# Generate SSL certificate
certbot --nginx -d api.mvs.kanousai.com --non-interactive --agree-tos --email <EMAIL>

# Verify auto-renewal
certbot renew --dry-run
```

## 🔧 **Automated Implementation Script**

I'll create an automated script to implement this entire strategy safely.
