# MVS-VR Vendor System Production Deployment
# Uses secure SSH connection and deploys to /vendor endpoint

param(
    [switch]$DryRun = $false
)

Write-Host "🚀 MVS-VR Vendor System Deployment" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

if ($DryRun) {
    Write-Host "🔍 DRY RUN MODE - No changes will be made" -ForegroundColor Cyan
}

# SSH Configuration
$SSH_KEY = "C:\Users\<USER>\.ssh\mvs-vr-doctl"
$SSH_OPTIONS = "-i `"$SSH_KEY`" -o StrictHostKeyChecking=no"

# Helper function for SSH commands
function Invoke-SSH {
    param($Command, $Description)
    
    Write-Host "`n📋 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would execute: $Command" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $fullCommand = "ssh $SSH_OPTIONS root@************** `"$Command`""
        $result = Invoke-Expression $fullCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ SSH error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Helper function for file copying
function Copy-File {
    param($LocalPath, $RemotePath, $Description)
    
    Write-Host "`n📦 $Description..." -ForegroundColor Blue
    
    if ($DryRun) {
        Write-Host "Would copy: $LocalPath -> $RemotePath" -ForegroundColor Cyan
        return $true
    }
    
    try {
        $fullCommand = "scp $SSH_OPTIONS `"$LocalPath`" root@**************:`"$RemotePath`""
        Invoke-Expression $fullCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Copy error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Verify admin system
Write-Host "`n🔍 Step 1: Verifying admin system..." -ForegroundColor Blue
try {
    $health = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($health.status -eq "ok") {
        Write-Host "✅ Admin system operational" -ForegroundColor Green
    } else {
        throw "Unexpected health status"
    }
} catch {
    Write-Host "❌ Admin system check failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test SSH connection
Write-Host "`n🔗 Step 2: Testing SSH connection..." -ForegroundColor Blue
$sshTest = Invoke-SSH "echo 'Connection successful'" "Testing SSH"
if (-not $sshTest) {
    Write-Host "❌ SSH connection failed" -ForegroundColor Red
    exit 1
}

# Step 3: Install dependencies
$deps = Invoke-SSH "cd /opt/mvs-vr && npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto" "Installing dependencies"
if (-not $deps) {
    Write-Host "❌ Dependency installation failed" -ForegroundColor Red
    exit 1
}

# Step 4: Create directories
$dirs = Invoke-SSH "mkdir -p /opt/mvs-vr/app/api /opt/mvs-vr/app/services /opt/mvs-vr/directus/extensions/interfaces/vendor-dashboard /opt/mvs-vr/logs" "Creating directories"
if (-not $dirs) {
    Write-Host "❌ Directory creation failed" -ForegroundColor Red
    exit 1
}

# Step 5: Deploy API files
$apiDeploy = Copy-File "app/api/vendor-registration.js" "/opt/mvs-vr/app/api/vendor-registration.js" "Deploying vendor API"
if (-not $apiDeploy) {
    Write-Host "❌ API deployment failed" -ForegroundColor Red
    exit 1
}

$emailDeploy = Copy-File "app/services/email-service.js" "/opt/mvs-vr/app/services/email-service.js" "Deploying email service"
if (-not $emailDeploy) {
    Write-Host "❌ Email service deployment failed" -ForegroundColor Red
    exit 1
}

# Step 6: Deploy dashboard extension
$extDeploy = Copy-File "directus/extensions/interfaces/vendor-dashboard/index.js" "/opt/mvs-vr/directus/extensions/interfaces/vendor-dashboard/index.js" "Deploying dashboard extension"
if (-not $extDeploy) {
    Write-Host "❌ Extension deployment failed" -ForegroundColor Red
    exit 1
}

# Step 7: Create environment configuration
Write-Host "`n📝 Step 7: Creating environment configuration..." -ForegroundColor Blue
$envConfig = @"
# Vendor System Configuration
NODE_ENV=production
VENDOR_API_PORT=3005
VENDOR_API_BASE_PATH=/vendor

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# CORS Configuration
CORS_ORIGIN=https://mvs.kanousai.com,https://api.mvs.kanousai.com

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# Directus Configuration
DIRECTUS_URL=https://api.mvs.kanousai.com
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=CS8aNYclCtm8lx18td0wgudQ34XG5Gm7
"@

if (-not $DryRun) {
    $envConfig | Out-File -FilePath "temp-env.conf" -Encoding UTF8
    $envDeploy = Copy-File "temp-env.conf" "/opt/mvs-vr/.env.vendor" "Deploying environment config"
    Remove-Item "temp-env.conf" -Force
    
    if (-not $envDeploy) {
        Write-Host "❌ Environment deployment failed" -ForegroundColor Red
        exit 1
    }
}

# Step 8: Set permissions
$perms = Invoke-SSH "chown -R root:root /opt/mvs-vr/app /opt/mvs-vr/directus/extensions && chmod -R 755 /opt/mvs-vr/app /opt/mvs-vr/directus/extensions" "Setting permissions"
if (-not $perms) {
    Write-Host "❌ Permission setting failed" -ForegroundColor Red
    exit 1
}

# Step 9: Configure NGINX for /vendor endpoint
Write-Host "`n🔧 Step 9: Configuring NGINX for /vendor endpoint..." -ForegroundColor Blue
$nginxConfig = @"
# Vendor API routing
location /vendor {
    proxy_pass http://localhost:3005;
    proxy_set_header Host `$host;
    proxy_set_header X-Real-IP `$remote_addr;
    proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto `$scheme;
    
    # CORS headers
    add_header Access-Control-Allow-Origin "https://mvs.kanousai.com" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
}
"@

if (-not $DryRun) {
    $nginxConfig | Out-File -FilePath "temp-nginx.conf" -Encoding UTF8
    $nginxDeploy = Copy-File "temp-nginx.conf" "/opt/mvs-vr/nginx-vendor.conf" "Deploying NGINX config"
    Remove-Item "temp-nginx.conf" -Force
    
    if ($nginxDeploy) {
        # Update NGINX configuration
        $nginxUpdate = Invoke-SSH "cp /etc/nginx/sites-available/api.mvs.kanousai.com /etc/nginx/sites-available/api.mvs.kanousai.com.backup && sed -i '/location \/ {/i\\n# Vendor API routing\nlocation /vendor {\n    proxy_pass http://localhost:3005;\n    proxy_set_header Host \$host;\n    proxy_set_header X-Real-IP \$remote_addr;\n    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n    proxy_set_header X-Forwarded-Proto \$scheme;\n}\n' /etc/nginx/sites-available/api.mvs.kanousai.com && nginx -t && systemctl reload nginx" "Updating NGINX"
        
        if (-not $nginxUpdate) {
            Write-Host "⚠️ NGINX update failed - API will run on port 3001 only" -ForegroundColor Yellow
        }
    }
}

# Step 10: Start vendor API with environment
$apiStart = Invoke-SSH "cd /opt/mvs-vr && export `$(cat .env.vendor | xargs) && nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 & echo `$! > vendor-api.pid" "Starting vendor API"
if (-not $apiStart) {
    Write-Host "❌ API startup failed" -ForegroundColor Red
    exit 1
}

# Step 11: Restart Directus
$directusRestart = Invoke-SSH "cd /opt/mvs-vr && docker-compose restart directus" "Restarting Directus"
if (-not $directusRestart) {
    Write-Host "❌ Directus restart failed" -ForegroundColor Red
    exit 1
}

# Step 12: Wait and verify
Write-Host "`n⏳ Waiting for services to start..." -ForegroundColor Blue
Start-Sleep -Seconds 30

# Verify deployment
Write-Host "`n✅ Verifying deployment..." -ForegroundColor Blue

# Check admin system
try {
    $adminCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/server/health" -TimeoutSec 10
    if ($adminCheck.status -eq "ok") {
        Write-Host "✅ Admin system operational" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Admin system check failed" -ForegroundColor Yellow
}

# Check vendor API directly on port 3005
$portCheck = Invoke-SSH "curl -s http://localhost:3005/health" "Checking vendor API on port 3005"
if ($portCheck) {
    Write-Host "✅ Vendor API running on port 3005" -ForegroundColor Green

    # Try to check if it's accessible via /vendor endpoint (if NGINX is configured)
    try {
        $vendorCheck = Invoke-RestMethod -Uri "https://api.mvs.kanousai.com/vendor/health" -TimeoutSec 10
        if ($vendorCheck.status -eq "ok") {
            Write-Host "✅ Vendor API also accessible at /vendor endpoint" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Vendor API not accessible via /vendor endpoint - only available on port 3005" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Vendor API not responding on port 3005" -ForegroundColor Red
}

# Final summary
Write-Host "`n🎉 Deployment Complete!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "✅ Vendor system deployed successfully" -ForegroundColor Green
Write-Host "✅ Admin system preserved" -ForegroundColor Green
Write-Host "✅ Email service configured with Supabase" -ForegroundColor Green

Write-Host "`n🔗 Service URLs:" -ForegroundColor Blue
Write-Host "   Admin: https://api.mvs.kanousai.com/admin" -ForegroundColor Cyan
Write-Host "   Vendor API: https://api.mvs.kanousai.com/vendor" -ForegroundColor Cyan
Write-Host "   Health: https://api.mvs.kanousai.com/server/health" -ForegroundColor Cyan

Write-Host "`n📋 Test Commands:" -ForegroundColor Blue
Write-Host "   System Health: curl https://api.mvs.kanousai.com/server/health" -ForegroundColor Cyan
Write-Host "   Vendor API Health: curl http://localhost:3005/health (on server)" -ForegroundColor Cyan
Write-Host "   Register: POST to http://localhost:3005/register (on server)" -ForegroundColor Cyan

Write-Host "`n🚀 Vendor system is live!" -ForegroundColor Green
