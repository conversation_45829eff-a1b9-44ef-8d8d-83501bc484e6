#!/bin/bash

# MVS-VR Vendor System Production Deployment Script
# Deploys the complete vendor system while preserving admin functionality

set -e  # Exit on any error

echo "🚀 MVS-VR Vendor System Production Deployment"
echo "============================================="

# Configuration
SERVER_IP="**************"
PROJECT_DIR="/opt/mvs-vr"
LOCAL_PROJECT_DIR="$(pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Step 1: Verify admin system is working
echo -e "\n${BLUE}🔍 Step 1: Verifying admin system status...${NC}"
if curl -f -s https://api.mvs.kanousai.com/server/health | grep -q '"status":"ok"'; then
    print_status "Admin system is healthy and operational"
else
    print_error "Admin system health check failed"
    exit 1
fi

# Step 2: Check SSH connectivity
echo -e "\n${BLUE}🔗 Step 2: Testing SSH connectivity...${NC}"
if ssh -o ConnectTimeout=10 root@$SERVER_IP "echo 'SSH connection successful'"; then
    print_status "SSH connection established"
else
    print_error "SSH connection failed"
    print_info "Please ensure SSH access is configured for root@$SERVER_IP"
    exit 1
fi

# Step 3: Create backup
echo -e "\n${BLUE}💾 Step 3: Creating system backup...${NC}"
ssh root@$SERVER_IP "mkdir -p $PROJECT_DIR/backups/vendor-deployment-\$(date +%Y%m%d_%H%M%S)"
print_status "Backup directory created"

# Step 4: Install dependencies
echo -e "\n${BLUE}📦 Step 4: Installing vendor system dependencies...${NC}"
ssh root@$SERVER_IP "cd $PROJECT_DIR && npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto"
if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Dependency installation failed"
    exit 1
fi

# Step 5: Create necessary directories
echo -e "\n${BLUE}📁 Step 5: Creating directory structure...${NC}"
ssh root@$SERVER_IP "mkdir -p $PROJECT_DIR/app/api $PROJECT_DIR/app/services $PROJECT_DIR/directus/extensions/interfaces/vendor-dashboard $PROJECT_DIR/logs"
print_status "Directory structure created"

# Step 6: Deploy vendor registration API
echo -e "\n${BLUE}📦 Step 6: Deploying vendor registration API...${NC}"
scp app/api/vendor-registration.js root@$SERVER_IP:$PROJECT_DIR/app/api/
if [ $? -eq 0 ]; then
    print_status "Vendor registration API deployed"
else
    print_error "API deployment failed"
    exit 1
fi

# Step 7: Deploy email service
echo -e "\n${BLUE}📧 Step 7: Deploying email service...${NC}"
scp app/services/email-service.js root@$SERVER_IP:$PROJECT_DIR/app/services/
if [ $? -eq 0 ]; then
    print_status "Email service deployed"
else
    print_error "Email service deployment failed"
    exit 1
fi

# Step 8: Deploy vendor dashboard extension
echo -e "\n${BLUE}🎛️ Step 8: Deploying vendor dashboard extension...${NC}"
scp directus/extensions/interfaces/vendor-dashboard/index.js root@$SERVER_IP:$PROJECT_DIR/directus/extensions/interfaces/vendor-dashboard/
if [ $? -eq 0 ]; then
    print_status "Vendor dashboard extension deployed"
else
    print_error "Dashboard extension deployment failed"
    exit 1
fi

# Step 9: Set proper permissions
echo -e "\n${BLUE}🔐 Step 9: Setting file permissions...${NC}"
ssh root@$SERVER_IP "chown -R root:root $PROJECT_DIR/app $PROJECT_DIR/directus/extensions && chmod -R 755 $PROJECT_DIR/app $PROJECT_DIR/directus/extensions"
print_status "File permissions set"

# Step 10: Create environment configuration
echo -e "\n${BLUE}📝 Step 10: Creating environment configuration...${NC}"
ssh root@$SERVER_IP "cat > $PROJECT_DIR/.env.vendor << 'EOF'
# Vendor System Configuration
VENDOR_API_PORT=3001
NODE_ENV=production

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5
EOF"
print_status "Environment configuration created"

# Step 11: Start vendor registration API
echo -e "\n${BLUE}🚀 Step 11: Starting vendor registration API...${NC}"
ssh root@$SERVER_IP "cd $PROJECT_DIR && nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 & echo \$! > vendor-api.pid"
print_status "Vendor registration API started"

# Step 12: Restart Directus to load extension
echo -e "\n${BLUE}🔄 Step 12: Restarting Directus to load vendor extension...${NC}"
ssh root@$SERVER_IP "cd $PROJECT_DIR && docker-compose restart directus"
print_status "Directus restarted"

# Step 13: Wait for services to stabilize
echo -e "\n${BLUE}⏳ Step 13: Waiting for services to stabilize...${NC}"
sleep 30
print_status "Services stabilization period completed"

# Step 14: Verify deployment
echo -e "\n${BLUE}✅ Step 14: Verifying deployment...${NC}"

# Check admin system still works
if curl -f -s https://api.mvs.kanousai.com/server/health | grep -q '"status":"ok"'; then
    print_status "Admin system remains operational"
else
    print_warning "Admin system health check shows issues"
fi

# Check vendor API
if ssh root@$SERVER_IP "curl -f -s http://localhost:3001/health" | grep -q '"status":"ok"'; then
    print_status "Vendor API is responding"
else
    print_warning "Vendor API health check failed"
fi

# Check Directus admin interface
if curl -f -s https://api.mvs.kanousai.com/admin | grep -q "Directus"; then
    print_status "Directus admin interface is accessible"
else
    print_warning "Directus admin interface check failed"
fi

# Final summary
echo -e "\n${GREEN}🎉 Vendor System Deployment Complete!${NC}"
echo "===================================="
print_status "Vendor Registration API deployed and running"
print_status "Vendor Dashboard Extension installed"
print_status "Email Service configured with Supabase"
print_status "Admin system preserved and operational"

echo -e "\n${BLUE}🔗 Service URLs:${NC}"
echo "   Admin Portal: https://api.mvs.kanousai.com/admin"
echo "   Directus Health: https://api.mvs.kanousai.com/server/health"
echo "   Vendor API: http://localhost:3001/health (internal)"

echo -e "\n${BLUE}📋 Next Steps:${NC}"
echo "   1. Test vendor registration flow"
echo "   2. Verify email delivery via Supabase"
echo "   3. Test vendor dashboard in Directus admin"
echo "   4. Monitor system logs for any issues"

echo -e "\n${GREEN}🚀 Vendor system is now live and ready for use!${NC}"

# Step 15: Test vendor registration
echo -e "\n${BLUE}🧪 Step 15: Testing vendor registration...${NC}"
TEST_EMAIL="test-vendor-$(date +%s)@example.com"
TEST_COMPANY="Test Company $(date +%s)"

ssh root@$SERVER_IP "curl -X POST http://localhost:3001/register -H 'Content-Type: application/json' -d '{
  \"company_name\": \"$TEST_COMPANY\",
  \"email\": \"$TEST_EMAIL\",
  \"contact_name\": \"Test User\",
  \"business_type\": \"retail\"
}'" > test_result.json

if grep -q '"success":true' test_result.json; then
    print_status "Vendor registration test passed"
else
    print_warning "Vendor registration test failed - check logs"
fi

rm -f test_result.json

echo -e "\n${GREEN}✅ Deployment verification completed!${NC}"
