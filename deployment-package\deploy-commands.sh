#!/bin/bash

# MVS-VR Vendor System Deployment Commands
# Execute these commands on the production server

echo "🚀 Starting MVS-VR Vendor System Deployment"
echo "==========================================="

# Step 1: Verify current system
echo "📋 Step 1: Verifying current system..."
curl -s https://api.mvs.kanousai.com/server/health
echo ""

# Step 2: Install dependencies
echo "📦 Step 2: Installing dependencies..."
cd /opt/mvs-vr
npm install @supabase/supabase-js axios express express-rate-limit helmet cors crypto

# Step 3: Create directories
echo "📁 Step 3: Creating directory structure..."
mkdir -p app/api app/services directus/extensions/interfaces/vendor-dashboard logs
mkdir -p backups/vendor-deployment-$(date +%Y%m%d_%H%M%S)

# Step 4: Create vendor registration API
echo "🔧 Step 4: Creating vendor registration API..."
cat > app/api/vendor-registration.js << 'EOF'
/**
 * MVS-VR Vendor Registration API
 * Handles vendor registration, verification, and management
 */

require('dotenv').config({ path: '.env.vendor' });

const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const EmailService = require('../services/email-service');

const app = express();
const PORT = process.env.VENDOR_API_PORT || 3001;

// Directus configuration
const DIRECTUS_URL = process.env.DIRECTUS_URL || 'https://api.mvs.kanousai.com';
const DIRECTUS_ADMIN_EMAIL = process.env.DIRECTUS_ADMIN_EMAIL;
const DIRECTUS_ADMIN_PASSWORD = process.env.DIRECTUS_ADMIN_PASSWORD;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['https://mvs.kanousai.com', 'https://api.mvs.kanousai.com'],
  credentials: true
}));

// Rate limiting
const registrationLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 5, // 5 requests per window
  message: {
    error: 'Too many registration attempts. Please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Vendor Registration API Class
class VendorRegistrationAPI {
  constructor() {
    this.adminToken = null;
    this.emailService = new EmailService();
  }

  async authenticateAdmin() {
    try {
      const response = await axios.post(`${DIRECTUS_URL}/auth/login`, {
        email: DIRECTUS_ADMIN_EMAIL,
        password: DIRECTUS_ADMIN_PASSWORD
      });
      
      this.adminToken = response.data.data.access_token;
      console.log('✅ Admin authentication successful');
      return true;
    } catch (error) {
      console.error('❌ Admin authentication failed:', error.message);
      return false;
    }
  }

  async createVendorRecord(registrationData, verificationToken) {
    try {
      // First, create a user account for the vendor
      const userData = {
        email: registrationData.email.toLowerCase().trim(),
        password: crypto.randomBytes(32).toString('hex'), // Random password, will be reset
        first_name: registrationData.contact_name.split(' ')[0] || 'Vendor',
        last_name: registrationData.contact_name.split(' ').slice(1).join(' ') || 'User',
        role: null, // Will be set to vendor role later
        status: 'invited'
      };

      const userResponse = await axios.post(`${DIRECTUS_URL}/users`, userData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      const userId = userResponse.data.data.id;

      const vendorData = {
        user_id: userId,
        company_name: registrationData.company_name.trim(),
        email: registrationData.email.toLowerCase().trim(),
        contact_name: registrationData.contact_name.trim(),
        business_type: registrationData.business_type,
        phone: registrationData.phone || null,
        website: registrationData.website || null,
        description: registrationData.description || null,
        status: 'pending',
        verification_token: verificationToken,
        verification_status: 'pending',
        onboarding_completed: false,
        subscription_tier: 'basic',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const response = await axios.post(`${DIRECTUS_URL}/items/vendors`, vendorData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      });

      return response.data.data;
    } catch (error) {
      console.error('❌ Vendor creation failed:', error.response?.data || error.message);
      throw error;
    }
  }

  async sendVerificationEmail(vendorData, verificationToken) {
    try {
      const result = await this.emailService.sendVendorVerificationEmail(vendorData, verificationToken);
      
      if (result.sent) {
        console.log('✅ Verification email sent successfully to:', vendorData.email);
      } else {
        console.error('❌ Failed to send verification email:', result.error);
      }
      
      return result;
    } catch (error) {
      console.error('❌ Email service error:', error.message);
      return {
        sent: false,
        error: error.message,
        message: 'Failed to send verification email'
      };
    }
  }

  validateRegistrationData(data) {
    const errors = [];

    if (!data.company_name || data.company_name.trim().length < 2) {
      errors.push('Company name is required and must be at least 2 characters');
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Valid email address is required');
    }

    if (!data.contact_name || data.contact_name.trim().length < 2) {
      errors.push('Contact name is required and must be at least 2 characters');
    }

    if (!data.business_type) {
      errors.push('Business type is required');
    }

    return errors;
  }
}

const vendorAPI = new VendorRegistrationAPI();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'vendor-registration-api',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Vendor registration endpoint
app.post('/register', registrationLimiter, async (req, res) => {
  try {
    console.log('📝 New vendor registration request received');
    
    // Validate input data
    const validationErrors = vendorAPI.validateRegistrationData(req.body);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    // Authenticate admin if needed
    if (!vendorAPI.adminToken) {
      const authSuccess = await vendorAPI.authenticateAdmin();
      if (!authSuccess) {
        return res.status(500).json({
          success: false,
          message: 'System authentication failed'
        });
      }
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');

    // Create vendor record
    const vendor = await vendorAPI.createVendorRecord(req.body, verificationToken);

    // Send verification email
    const emailResult = await vendorAPI.sendVerificationEmail(req.body, verificationToken);

    res.status(201).json({
      success: true,
      message: 'Vendor registration initiated successfully',
      data: {
        vendor_id: vendor.id,
        company_name: vendor.company_name,
        email: vendor.email,
        status: vendor.status,
        verification_required: true
      },
      email_status: emailResult
    });

  } catch (error) {
    console.error('❌ Registration error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Email verification endpoint
app.get('/verify', async (req, res) => {
  try {
    const { token, email } = req.query;

    if (!token || !email) {
      return res.status(400).json({
        success: false,
        message: 'Verification token and email are required'
      });
    }

    // Authenticate admin if needed
    if (!vendorAPI.adminToken) {
      const authSuccess = await vendorAPI.authenticateAdmin();
      if (!authSuccess) {
        return res.status(500).json({
          success: false,
          message: 'System authentication failed'
        });
      }
    }

    // Find vendor by token and email
    const vendorResponse = await axios.get(`${DIRECTUS_URL}/items/vendors`, {
      params: {
        filter: {
          verification_token: { _eq: token },
          email: { _eq: email.toLowerCase() }
        }
      },
      headers: { Authorization: `Bearer ${vendorAPI.adminToken}` }
    });

    if (!vendorResponse.data.data || vendorResponse.data.data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification token or email'
      });
    }

    const vendor = vendorResponse.data.data[0];

    if (vendor.verification_status === 'verified') {
      return res.status(200).json({
        success: true,
        message: 'Email already verified',
        vendor: {
          id: vendor.id,
          company_name: vendor.company_name,
          email: vendor.email,
          status: vendor.status
        }
      });
    }

    // Update vendor verification status
    await axios.patch(`${DIRECTUS_URL}/items/vendors/${vendor.id}`, {
      verification_status: 'verified',
      status: 'approved',
      verification_token: null,
      updated_at: new Date().toISOString()
    }, {
      headers: { Authorization: `Bearer ${vendorAPI.adminToken}` }
    });

    // Send welcome email
    try {
      await vendorAPI.emailService.sendVendorWelcomeEmail(vendor);
      console.log('✅ Welcome email sent to:', vendor.email);
    } catch (error) {
      console.error('⚠️ Failed to send welcome email:', error.message);
      // Don't fail the verification if welcome email fails
    }

    res.status(200).json({
      success: true,
      message: 'Vendor verification completed successfully',
      data: {
        vendor_id: vendor.id,
        company_name: vendor.company_name,
        status: 'approved',
        next_steps: [
          'Your vendor account is now active',
          'You can now access the vendor portal',
          'Complete your profile setup to get started'
        ]
      }
    });

  } catch (error) {
    console.error('❌ Verification error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Verification failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Vendor Registration API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📝 Registration: http://localhost:${PORT}/register`);
  console.log(`📧 Verification: http://localhost:${PORT}/verify`);
  
  // Initial admin authentication
  vendorAPI.authenticateAdmin();
});

module.exports = app;
EOF

echo "✅ Vendor registration API created"

# Step 5: Create email service
echo "📧 Step 5: Creating email service..."
cat > app/services/email-service.js << 'EOF'
/**
 * MVS-VR Email Service
 * Handles email sending for vendor verification and notifications via Supabase
 */

const { createClient } = require('@supabase/supabase-js');

class EmailService {
  constructor() {
    // Initialize Supabase client for email sending
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
      this.isConfigured = true;
      console.log('✅ Email service configured with Supabase');
    } else {
      console.warn('⚠️ Supabase not configured. Email sending will be simulated.');
      this.isConfigured = false;
    }
    
    this.fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
    this.replyToEmail = process.env.EMAIL_REPLY_TO || '<EMAIL>';
  }

  /**
   * Send vendor verification email
   */
  async sendVendorVerificationEmail(vendorData, verificationToken) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'https://mvs.kanousai.com'}/vendor/verify?token=${verificationToken}&email=${encodeURIComponent(vendorData.email)}`;
    
    const emailData = {
      to: vendorData.email,
      from: {
        email: this.fromEmail,
        name: 'MVS-VR Platform'
      },
      replyTo: this.replyToEmail,
      subject: 'Verify Your MVS-VR Vendor Account',
      html: this.generateVerificationEmailHTML(vendorData, verificationUrl),
      text: this.generateVerificationEmailText(vendorData, verificationUrl),
      company_name: vendorData.company_name,
      verification_url: verificationUrl
    };

    return await this.sendEmail(emailData);
  }

  /**
   * Send welcome email after verification
   */
  async sendVendorWelcomeEmail(vendorData) {
    const emailData = {
      to: vendorData.email,
      from: {
        email: this.fromEmail,
        name: 'MVS-VR Platform'
      },
      replyTo: this.replyToEmail,
      subject: 'Welcome to MVS-VR - Your Account is Verified!',
      html: this.generateWelcomeEmailHTML(vendorData),
      text: this.generateWelcomeEmailText(vendorData)
    };

    return await this.sendEmail(emailData);
  }

  /**
   * Core email sending method using Supabase
   */
  async sendEmail(emailData) {
    try {
      if (this.isConfigured && this.supabase) {
        // Use Supabase Auth to send emails
        // Note: This uses Supabase's built-in email functionality
        console.log('📧 Sending email via Supabase Auth...');
        console.log(`   To: ${emailData.to}`);
        console.log(`   Subject: ${emailData.subject}`);
        
        // For vendor verification, we'll use Supabase's email system
        // This is a placeholder - actual implementation depends on Supabase setup
        const result = await this.supabase.auth.admin.inviteUserByEmail(emailData.to, {
          data: {
            vendor_verification: true,
            company_name: emailData.company_name || 'Unknown Company',
            verification_url: emailData.verification_url || ''
          }
        });
        
        if (result.error) {
          throw new Error(result.error.message);
        }
        
        console.log('✅ Email sent successfully via Supabase:', emailData.to);
        return {
          sent: true,
          messageId: result.data?.user?.id || 'supabase-' + Date.now(),
          message: 'Email sent successfully via Supabase',
          provider: 'supabase'
        };
      } else {
        // Simulate email sending for development/testing
        console.log('📧 Email Simulation (Supabase not configured):');
        console.log(`   To: ${emailData.to}`);
        console.log(`   Subject: ${emailData.subject}`);
        console.log(`   From: ${emailData.from.email || emailData.from}`);
        
        return {
          sent: true,
          messageId: 'simulated-' + Date.now(),
          message: 'Email simulated (Supabase not configured)',
          simulatedData: emailData,
          provider: 'simulation'
        };
      }
    } catch (error) {
      console.error('❌ Email sending failed:', error.message);
      return {
        sent: false,
        error: error.message,
        message: 'Failed to send email',
        provider: this.isConfigured ? 'supabase' : 'simulation'
      };
    }
  }

  /**
   * Generate HTML email template for verification
   */
  generateVerificationEmailHTML(vendorData, verificationUrl) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your MVS-VR Vendor Account</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Welcome to MVS-VR Platform</h1>
            <p>Virtual Reality Commerce Platform</p>
        </div>
        <div class="content">
            <h2>Hello ${vendorData.contact_name},</h2>
            <p>Thank you for registering <strong>${vendorData.company_name}</strong> with the MVS-VR Platform!</p>
            <p>To complete your registration and start creating VR experiences, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center;">
                <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </div>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">${verificationUrl}</p>
            
            <h3>What's Next?</h3>
            <ul>
                <li>Access your vendor dashboard</li>
                <li>Upload your products and 3D models</li>
                <li>Create immersive VR showrooms</li>
                <li>Manage your VR commerce experiences</li>
            </ul>
            
            <p>If you didn't create this account, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>© 2025 MVS-VR Platform. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${this.replyToEmail}">${this.replyToEmail}</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate plain text email for verification
   */
  generateVerificationEmailText(vendorData, verificationUrl) {
    return `
Welcome to MVS-VR Platform!

Hello ${vendorData.contact_name},

Thank you for registering ${vendorData.company_name} with the MVS-VR Platform!

To complete your registration and start creating VR experiences, please verify your email address by visiting:

${verificationUrl}

What's Next?
- Access your vendor dashboard
- Upload your products and 3D models
- Create immersive VR showrooms
- Manage your VR commerce experiences

If you didn't create this account, please ignore this email.

© 2025 MVS-VR Platform. All rights reserved.
Need help? Contact us at ${this.replyToEmail}
`;
  }

  /**
   * Generate HTML welcome email template
   */
  generateWelcomeEmailHTML(vendorData) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MVS-VR Platform</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Account Verified!</h1>
            <p>Welcome to MVS-VR Platform</p>
        </div>
        <div class="content">
            <h2>Congratulations ${vendorData.contact_name}!</h2>
            <p>Your vendor account for <strong>${vendorData.company_name}</strong> has been successfully verified and is now active.</p>
            
            <div style="text-align: center;">
                <a href="https://api.mvs.kanousai.com/admin" class="button">Access Your Dashboard</a>
            </div>
            
            <h3>Getting Started:</h3>
            <ol>
                <li><strong>Set Up Your Profile:</strong> Complete your company information</li>
                <li><strong>Upload Products:</strong> Add your 3D models and product catalog</li>
                <li><strong>Create VR Experiences:</strong> Build immersive showrooms</li>
                <li><strong>Go Live:</strong> Publish your VR commerce experiences</li>
            </ol>
            
            <p>Our team is here to help you succeed. Don't hesitate to reach out if you need assistance!</p>
        </div>
        <div class="footer">
            <p>© 2025 MVS-VR Platform. All rights reserved.</p>
            <p>Need help? Contact us at <a href="mailto:${this.replyToEmail}">${this.replyToEmail}</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate plain text welcome email
   */
  generateWelcomeEmailText(vendorData) {
    return `
Account Verified - Welcome to MVS-VR Platform!

Congratulations ${vendorData.contact_name}!

Your vendor account for ${vendorData.company_name} has been successfully verified and is now active.

Access your dashboard: https://api.mvs.kanousai.com/admin

Getting Started:
1. Set Up Your Profile: Complete your company information
2. Upload Products: Add your 3D models and product catalog
3. Create VR Experiences: Build immersive showrooms
4. Go Live: Publish your VR commerce experiences

Our team is here to help you succeed. Don't hesitate to reach out if you need assistance!

© 2025 MVS-VR Platform. All rights reserved.
Need help? Contact us at ${this.replyToEmail}
`;
  }
}

module.exports = EmailService;
EOF

echo "✅ Email service created"

# Step 6: Create vendor dashboard extension
echo "🎛️ Step 6: Creating vendor dashboard extension..."
cat > directus/extensions/interfaces/vendor-dashboard/index.js << 'EOF'
/**
 * MVS-VR Vendor Dashboard Interface for Directus
 * Custom interface for vendor-specific operations and management
 */

import { defineInterface } from '@directus/extensions-sdk';

export default defineInterface({
  id: 'vendor-dashboard',
  name: 'Vendor Dashboard',
  icon: 'dashboard',
  description: 'Comprehensive vendor management dashboard with VR commerce features',
  component: VendorDashboard,
  options: [
    {
      field: 'vendor_id',
      name: 'Vendor ID',
      type: 'string',
      meta: {
        interface: 'input',
        width: 'half',
        note: 'Current vendor ID for data filtering'
      }
    },
    {
      field: 'show_analytics',
      name: 'Show Analytics',
      type: 'boolean',
      meta: {
        interface: 'boolean',
        width: 'half',
        options: {
          label: 'Display analytics dashboard'
        }
      },
      schema: {
        default_value: true
      }
    },
    {
      field: 'show_vr_features',
      name: 'Show VR Features',
      type: 'boolean',
      meta: {
        interface: 'boolean',
        width: 'half',
        options: {
          label: 'Display VR experience management'
        }
      },
      schema: {
        default_value: true
      }
    }
  ],
  types: ['alias'],
  localTypes: ['presentation'],
  group: 'presentation',
  relational: false
});

// Vue component for the vendor dashboard
const VendorDashboard = {
  template: \`
    <div class="vendor-dashboard">
      <div class="dashboard-header">
        <h2 class="dashboard-title">
          <v-icon name="store" />
          Vendor Dashboard
        </h2>
        <div class="vendor-info" v-if="vendorData">
          <span class="vendor-name">{{ vendorData.company_name }}</span>
          <span class="vendor-status" :class="'status-' + vendorData.status">
            {{ vendorData.status }}
          </span>
        </div>
      </div>

      <div class="dashboard-stats" v-if="showAnalytics">
        <div class="stat-card">
          <div class="stat-value">{{ stats.products }}</div>
          <div class="stat-label">Products</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.showrooms }}</div>
          <div class="stat-label">Showrooms</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.vr_experiences }}</div>
          <div class="stat-label">VR Experiences</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.orders }}</div>
          <div class="stat-label">Orders</div>
        </div>
      </div>

      <div class="dashboard-actions">
        <v-button @click="navigateToProducts" icon="inventory">
          Manage Products
        </v-button>
        <v-button @click="navigateToShowrooms" icon="view_in_ar">
          VR Showrooms
        </v-button>
        <v-button @click="navigateToExperiences" icon="3d_rotation" v-if="showVrFeatures">
          VR Experiences
        </v-button>
        <v-button @click="navigateToAnalytics" icon="analytics" v-if="showAnalytics">
          Analytics
        </v-button>
      </div>

      <div class="dashboard-recent" v-if="recentActivity.length > 0">
        <h3>Recent Activity</h3>
        <div class="activity-list">
          <div v-for="activity in recentActivity" :key="activity.id" class="activity-item">
            <v-icon :name="activity.icon" />
            <span class="activity-text">{{ activity.description }}</span>
            <span class="activity-time">{{ formatTime(activity.created_at) }}</span>
          </div>
        </div>
      </div>
    </div>
  \`,

  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    vendorId: {
      type: String,
      default: null
    },
    showAnalytics: {
      type: Boolean,
      default: true
    },
    showVrFeatures: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      vendorData: null,
      stats: {
        products: 0,
        showrooms: 0,
        vr_experiences: 0,
        orders: 0
      },
      recentActivity: [],
      loading: false
    };
  },

  async mounted() {
    await this.loadVendorData();
    await this.loadStats();
    await this.loadRecentActivity();
  },

  methods: {
    async loadVendorData() {
      if (!this.vendorId) return;

      try {
        const response = await this.\$api.get(\`/items/vendors/\${this.vendorId}\`);
        this.vendorData = response.data.data;
      } catch (error) {
        console.error('Error loading vendor data:', error);
      }
    },

    async loadStats(vendorId) {
      try {
        const [products, showrooms, vrExperiences] = await Promise.all([
          this.\$api.get(\`/items/products?filter[vendor_id][_eq]=\${vendorId}&aggregate[count]=*\`),
          this.\$api.get(\`/items/showroom_layouts?filter[vendor_id][_eq]=\${vendorId}&aggregate[count]=*\`),
          this.\$api.get(\`/items/vr_experiences?filter[vendor_id][_eq]=\${vendorId}&aggregate[count]=*\`)
        ]);

        this.stats = {
          products: products.data.data[0]?.count || 0,
          showrooms: showrooms.data.data[0]?.count || 0,
          vr_experiences: vrExperiences.data.data[0]?.count || 0,
          orders: 0 // TODO: Implement orders count
        };
      } catch (error) {
        console.error('Error loading stats:', error);
        this.stats = { products: 0, showrooms: 0, vr_experiences: 0, orders: 0 };
      }
    },

    async loadRecentActivity() {
      if (!this.vendorId) return;

      try {
        // Load recent activity for this vendor
        const response = await this.\$api.get(\`/items/vendor_activity?filter[vendor_id][_eq]=\${this.vendorId}&sort=-created_at&limit=5\`);
        this.recentActivity = response.data.data || [];
      } catch (error) {
        console.error('Error loading recent activity:', error);
        this.recentActivity = [];
      }
    },

    navigateToProducts() {
      this.\$router.push('/content/products');
    },

    navigateToShowrooms() {
      this.\$router.push('/content/showroom_layouts');
    },

    navigateToExperiences() {
      this.\$router.push('/content/vr_experiences');
    },

    navigateToAnalytics() {
      this.\$router.push('/insights/analytics');
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleDateString();
    }
  }
};
EOF

echo "✅ Vendor dashboard extension created"

# Step 7: Create environment configuration
echo "📝 Step 6: Creating environment configuration..."
cat > .env.vendor << 'EOF'
# Vendor System Configuration
VENDOR_API_PORT=3001
NODE_ENV=production

# Supabase Configuration
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.YQJWVLBhKJWJJOJJOJJOJJOJJOJJOJJOJJOJJOJJOJJO

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# Directus Configuration
DIRECTUS_URL=https://api.mvs.kanousai.com
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=CS8aNYclCtm8lx18td0wgudQ34XG5Gm7
EOF

echo "✅ Environment configuration created"

# Step 7: Set permissions
echo "🔐 Step 7: Setting file permissions..."
chown -R root:root app/ directus/extensions/
chmod -R 755 app/ directus/extensions/
chmod +x app/api/vendor-registration.js

# Step 9: Start vendor API
echo "🚀 Step 9: Starting vendor registration API..."
nohup node app/api/vendor-registration.js > logs/vendor-api.log 2>&1 &
echo $! > vendor-api.pid

# Wait for API to start
sleep 5

# Step 10: Restart Directus to load extension
echo "🔄 Step 10: Restarting Directus to load vendor extension..."
docker-compose restart directus

# Wait for Directus to restart
sleep 30

# Step 11: Test deployment
echo "🧪 Step 11: Testing deployment..."

# Test admin system
echo "Testing admin system..."
curl -s https://api.mvs.kanousai.com/server/health

# Test vendor API
echo "Testing vendor API..."
curl -s http://localhost:3001/health

# Test vendor registration
echo "Testing vendor registration..."
curl -X POST http://localhost:3001/register \
  -H "Content-Type: application/json" \
  -d '{
    "company_name": "Test Company",
    "email": "<EMAIL>",
    "contact_name": "Test User",
    "business_type": "retail"
  }'

echo ""
echo "✅ Vendor system deployment completed!"
echo "📊 Check logs: tail -f logs/vendor-api.log"
echo "🔗 API Health: curl http://localhost:3001/health"
echo "🔗 Admin Portal: https://api.mvs.kanousai.com/admin"
EOF

chmod +x deployment-package/deploy-commands.sh

echo "✅ Deployment package created successfully!"
