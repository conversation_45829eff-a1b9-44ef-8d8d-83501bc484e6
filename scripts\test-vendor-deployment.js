#!/usr/bin/env node

/**
 * MVS-VR Vendor System Deployment Test
 * Tests the deployed vendor system at https://api.mvs.kanousai.com/vendor
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://api.mvs.kanousai.com';
const VENDOR_ENDPOINT = `${BASE_URL}/vendor`;

async function testDeployment() {
  console.log('🧪 Testing MVS-VR Vendor System Deployment');
  console.log('==========================================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Vendor Endpoint: ${VENDOR_ENDPOINT}`);
  
  let passed = 0;
  let failed = 0;
  
  // Test 1: Admin system health
  console.log('\n📋 Test 1: Admin System Health');
  try {
    const response = await axios.get(`${BASE_URL}/server/health`, { timeout: 10000 });
    if (response.status === 200 && response.data.status === 'ok') {
      console.log('✅ Admin system is healthy');
      passed++;
    } else {
      console.log('❌ Admin system health check failed');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Admin system error: ${error.message}`);
    failed++;
  }
  
  // Test 2: Vendor API health
  console.log('\n📋 Test 2: Vendor API Health');
  try {
    const response = await axios.get(`${VENDOR_ENDPOINT}/health`, { timeout: 10000 });
    if (response.status === 200 && response.data.status === 'ok') {
      console.log('✅ Vendor API is healthy');
      console.log(`   Service: ${response.data.service}`);
      console.log(`   Version: ${response.data.version}`);
      passed++;
    } else {
      console.log('❌ Vendor API health check failed');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Vendor API error: ${error.message}`);
    failed++;
  }
  
  // Test 3: Directus admin interface
  console.log('\n📋 Test 3: Directus Admin Interface');
  try {
    const response = await axios.get(`${BASE_URL}/admin`, { timeout: 10000 });
    if (response.status === 200 && response.data.includes('Directus')) {
      console.log('✅ Directus admin interface is accessible');
      passed++;
    } else {
      console.log('❌ Directus admin interface check failed');
      failed++;
    }
  } catch (error) {
    console.log(`❌ Directus admin error: ${error.message}`);
    failed++;
  }
  
  // Test 4: Vendor registration (with test data)
  console.log('\n📋 Test 4: Vendor Registration');
  const testVendor = {
    company_name: `Test Company ${Date.now()}`,
    email: `test-${Date.now()}@example.com`,
    contact_name: 'Test User',
    business_type: 'retail',
    phone: '******-0123'
  };
  
  try {
    const response = await axios.post(`${VENDOR_ENDPOINT}/register`, testVendor, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000
    });
    
    if (response.status === 201 && response.data.success === true) {
      console.log('✅ Vendor registration successful');
      console.log(`   Vendor ID: ${response.data.data.vendor_id}`);
      console.log(`   Status: ${response.data.data.status}`);
      console.log(`   Email Status: ${response.data.email_status.sent ? 'Sent' : 'Failed'}`);
      passed++;
      
      // Test 5: Email verification (if we got a token)
      if (response.data.verification_token) {
        console.log('\n📋 Test 5: Email Verification');
        try {
          const verifyUrl = `${VENDOR_ENDPOINT}/verify?token=${response.data.verification_token}&email=${encodeURIComponent(testVendor.email)}`;
          const verifyResponse = await axios.get(verifyUrl, { timeout: 10000 });
          
          if (verifyResponse.status === 200 && verifyResponse.data.success === true) {
            console.log('✅ Email verification successful');
            console.log(`   Vendor Status: ${verifyResponse.data.data.status}`);
            passed++;
          } else {
            console.log('❌ Email verification failed');
            failed++;
          }
        } catch (error) {
          console.log(`❌ Email verification error: ${error.message}`);
          failed++;
        }
      } else {
        console.log('\n📋 Test 5: Email Verification - Skipped (no token)');
      }
    } else {
      console.log('❌ Vendor registration failed');
      console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ Vendor registration error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    failed++;
  }
  
  // Test 6: Rate limiting
  console.log('\n📋 Test 6: Rate Limiting');
  try {
    const requests = [];
    for (let i = 0; i < 7; i++) {
      requests.push(
        axios.post(`${VENDOR_ENDPOINT}/register`, {
          company_name: `Rate Test ${i}`,
          email: `ratetest${i}@example.com`,
          contact_name: 'Rate Test',
          business_type: 'retail'
        }, {
          headers: { 'Content-Type': 'application/json' },
          timeout: 5000
        }).catch(error => error.response)
      );
    }
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.some(response => 
      response && response.status === 429
    );
    
    if (rateLimited) {
      console.log('✅ Rate limiting is working');
      passed++;
    } else {
      console.log('⚠️ Rate limiting may not be configured');
      // Don't count as failed since it's not critical
    }
  } catch (error) {
    console.log(`⚠️ Rate limiting test error: ${error.message}`);
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('🎯 DEPLOYMENT TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${passed + failed}`);
  console.log(`Passed: ${passed} ✅`);
  console.log(`Failed: ${failed} ❌`);
  console.log(`Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Deployment is successful!');
    console.log('\n🔗 Service URLs:');
    console.log(`   Admin Portal: ${BASE_URL}/admin`);
    console.log(`   Vendor API: ${VENDOR_ENDPOINT}`);
    console.log(`   Health Check: ${BASE_URL}/server/health`);
    
    console.log('\n📋 Next Steps:');
    console.log('   1. Test vendor registration flow manually');
    console.log('   2. Verify email delivery via Supabase');
    console.log('   3. Test vendor dashboard in Directus');
    console.log('   4. Begin vendor onboarding process');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the deployment.');
    console.log('Review the error messages above and verify:');
    console.log('   - All services are running');
    console.log('   - NGINX configuration is correct');
    console.log('   - Environment variables are set');
    console.log('   - Database connections are working');
  }
  
  console.log('\n' + '='.repeat(50));
  
  // Exit with appropriate code
  process.exit(failed > 0 ? 1 : 0);
}

// Run the test
if (require.main === module) {
  testDeployment().catch(error => {
    console.error('❌ Test suite error:', error.message);
    process.exit(1);
  });
}

module.exports = { testDeployment };
